-- Update invoice system with new requirements

-- 1. Add commission fields to sales_invoices table
ALTER TABLE sales_invoices 
ADD COLUMN IF NOT EXISTS is_commission_sale BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS commission_percentage DECIMAL(5,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS commission_amount DECIMAL(10,2) DEFAULT 0.00;

-- 2. Add return status to invoice status enum
-- First, check current enum values
DO $$
BEGIN
    -- Add 'returned' to invoice_status enum if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'returned' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'invoice_status')
    ) THEN
        ALTER TYPE invoice_status ADD VALUE 'returned';
    END IF;
END$$;

-- 3. Add return fields to sales_invoices table
ALTER TABLE sales_invoices 
ADD COLUMN IF NOT EXISTS return_amount DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS return_reason TEXT,
ADD COLUMN IF NOT EXISTS returned_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS returned_by UUID REFERENCES auth.users(id);

-- 4. Make due_date optional (it already is, but ensure it)
ALTER TABLE sales_invoices 
ALTER COLUMN due_date DROP NOT NULL;

-- 5. Add comments to explain the new fields
COMMENT ON COLUMN sales_invoices.is_commission_sale IS 'Whether this is a commission sale (5%)';
COMMENT ON COLUMN sales_invoices.commission_percentage IS 'Commission percentage (usually 5%)';
COMMENT ON COLUMN sales_invoices.commission_amount IS 'Commission amount in currency';
COMMENT ON COLUMN sales_invoices.return_amount IS 'Amount returned to customer';
COMMENT ON COLUMN sales_invoices.return_reason IS 'Reason for return';
COMMENT ON COLUMN sales_invoices.returned_at IS 'When the invoice was returned';
COMMENT ON COLUMN sales_invoices.returned_by IS 'Who processed the return';

-- 6. Update existing invoices to have default values
UPDATE sales_invoices 
SET 
  is_commission_sale = FALSE,
  commission_percentage = 0.00,
  commission_amount = 0.00,
  return_amount = 0.00
WHERE 
  is_commission_sale IS NULL 
  OR commission_percentage IS NULL 
  OR commission_amount IS NULL
  OR return_amount IS NULL;

-- 7. Create function to handle stock restoration on return
CREATE OR REPLACE FUNCTION restore_stock_on_return()
RETURNS TRIGGER AS $$
BEGIN
    -- If invoice status changed to 'returned', restore stock
    IF OLD.status != 'returned' AND NEW.status = 'returned' THEN
        -- Restore stock for all items in this invoice
        UPDATE products 
        SET stock = stock + ii.quantity
        FROM invoice_items ii
        WHERE ii.invoice_id = NEW.id 
        AND products.id = ii.product_id;
        
        -- Log the return
        NEW.returned_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 8. Create trigger for stock restoration
DROP TRIGGER IF EXISTS trigger_restore_stock_on_return ON sales_invoices;
CREATE TRIGGER trigger_restore_stock_on_return
    BEFORE UPDATE ON sales_invoices
    FOR EACH ROW
    EXECUTE FUNCTION restore_stock_on_return();

-- 9. Create index for better performance on status queries
CREATE INDEX IF NOT EXISTS idx_sales_invoices_status ON sales_invoices(status);
CREATE INDEX IF NOT EXISTS idx_sales_invoices_payment_type ON sales_invoices(payment_type);

-- 10. Update RLS policies if needed (ensure users can update their invoices)
-- This assumes you have proper RLS policies in place
