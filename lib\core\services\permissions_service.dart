import 'package:shared_preferences/shared_preferences.dart';

class PermissionsService {
  static const String _appointmentsKey = 'appointments_permission';
  static const String _patientsKey = 'patients_permission';
  static const String _productsKey = 'products_permission';
  static const String _articlesKey = 'articles_permission';
  static const String _employeesKey = 'employees_permission';
  static const String _schedulesKey = 'schedules_permission';
  static const String _reportsKey = 'reports_permission';
  static const String _adminPasswordKey = 'admin_password';
  static const String _adminPassword = 'RX12345@@';

  // Get SharedPreferences instance
  static Future<SharedPreferences> get _prefs async {
    return await SharedPreferences.getInstance();
  }

  // Check if admin password is correct
  static bool isValidAdminPassword(String password) {
    return password == _adminPassword;
  }

  // Get appointments permission (default: true)
  static Future<bool> getAppointmentsPermission() async {
    final prefs = await _prefs;
    return prefs.getBool(_appointmentsKey) ?? true;
  }

  // Set appointments permission
  static Future<void> setAppointmentsPermission(bool enabled) async {
    final prefs = await _prefs;
    await prefs.setBool(_appointmentsKey, enabled);
  }

  // Get patients permission (default: true)
  static Future<bool> getPatientsPermission() async {
    final prefs = await _prefs;
    return prefs.getBool(_patientsKey) ?? true;
  }

  // Set patients permission
  static Future<void> setPatientsPermission(bool enabled) async {
    final prefs = await _prefs;
    await prefs.setBool(_patientsKey, enabled);
  }

  // Get products permission (default: true)
  static Future<bool> getProductsPermission() async {
    final prefs = await _prefs;
    return prefs.getBool(_productsKey) ?? true;
  }

  // Set products permission
  static Future<void> setProductsPermission(bool enabled) async {
    final prefs = await _prefs;
    await prefs.setBool(_productsKey, enabled);
  }

  // Get articles permission (default: true)
  static Future<bool> getArticlesPermission() async {
    final prefs = await _prefs;
    return prefs.getBool(_articlesKey) ?? true;
  }

  // Set articles permission
  static Future<void> setArticlesPermission(bool enabled) async {
    final prefs = await _prefs;
    await prefs.setBool(_articlesKey, enabled);
  }

  // Get employees permission (default: true for admin, false for others)
  static Future<bool> getEmployeesPermission() async {
    final prefs = await _prefs;
    return prefs.getBool(_employeesKey) ?? true;
  }

  // Set employees permission
  static Future<void> setEmployeesPermission(bool enabled) async {
    final prefs = await _prefs;
    await prefs.setBool(_employeesKey, enabled);
  }

  // Get schedules permission (default: true for admin/specialist, false for others)
  static Future<bool> getSchedulesPermission() async {
    final prefs = await _prefs;
    return prefs.getBool(_schedulesKey) ?? true;
  }

  // Set schedules permission
  static Future<void> setSchedulesPermission(bool enabled) async {
    final prefs = await _prefs;
    await prefs.setBool(_schedulesKey, enabled);
  }

  // Get reports permission (default: true for admin, false for others)
  static Future<bool> getReportsPermission() async {
    final prefs = await _prefs;
    return prefs.getBool(_reportsKey) ?? true;
  }

  // Set reports permission
  static Future<void> setReportsPermission(bool enabled) async {
    final prefs = await _prefs;
    await prefs.setBool(_reportsKey, enabled);
  }

  // Get all permissions
  static Future<Map<String, bool>> getAllPermissions() async {
    return {
      'appointments': await getAppointmentsPermission(),
      'patients': await getPatientsPermission(),
      'products': await getProductsPermission(),
      'articles': await getArticlesPermission(),
      'employees': await getEmployeesPermission(),
      'schedules': await getSchedulesPermission(),
      'reports': await getReportsPermission(),
    };
  }

  // Check if user is admin (all permissions enabled)
  static Future<bool> isAdmin() async {
    final permissions = await getAllPermissions();
    return permissions.values.every((permission) => permission);
  }

  // Reset all permissions to default (all enabled)
  static Future<void> resetPermissions() async {
    await setAppointmentsPermission(true);
    await setPatientsPermission(true);
    await setProductsPermission(true);
    await setArticlesPermission(true);
    await setEmployeesPermission(true);
    await setSchedulesPermission(true);
    await setReportsPermission(true);
  }

  // Get permissions based on employee type
  static Future<Map<String, bool>> getPermissionsByEmployeeType(String employeeType) async {
    switch (employeeType) {
      case 'super_admin':
        return {
          'appointments': true,
          'patients': true,
          'products': true,
          'articles': true,
          'employees': true,
          'schedules': true,
          'reports': true,
        };
      case 'admin':
        return {
          'appointments': true,
          'patients': true,
          'products': true,
          'articles': true,
          'employees': true,
          'schedules': true,
          'reports': true,
        };
      case 'receptionist':
        return {
          'appointments': true,
          'patients': true,
          'products': false,
          'articles': false,
          'employees': false,
          'schedules': false,
          'reports': false,
        };
      case 'specialist':
        return {
          'appointments': true,
          'patients': true,
          'products': false,
          'articles': false,
          'employees': false,
          'schedules': true, // Can manage their own schedule
          'reports': false,
        };
      default:
        return await getAllPermissions();
    }
  }

  // Check if employee has specific permission
  static Future<bool> hasPermission(String employeeType, String permission) async {
    final permissions = await getPermissionsByEmployeeType(employeeType);
    return permissions[permission] ?? false;
  }
}
