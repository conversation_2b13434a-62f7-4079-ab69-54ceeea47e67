import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/widgets/permission_wrapper.dart';
import '../../../appointments/presentation/pages/appointments_page.dart';
import '../../../patients/presentation/pages/patients_page.dart';
import '../../../products/presentation/pages/products_page.dart';

import '../../../employees/presentation/pages/employees_main_page.dart';
import '../../../sales/presentation/pages/sales_main_page.dart';
import '../../../profile/presentation/pages/settings_page.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  int _currentIndex = 0;

  // Track which pages have been visited
  final Set<int> _visitedPages = {0}; // Start with appointments page visited

  List<Widget> get _pages => [
    PermissionWrapper(
      permissionKey: 'appointments',
      featureName: 'الحجوزات',
      child: AppointmentsPage(isVisible: _currentIndex == 0),
    ),
    PermissionWrapper(
      permissionKey: 'sales',
      featureName: 'المبيعات',
      child: SalesMainPage(
        isVisible: _currentIndex == 1,
        hasBeenVisited: _visitedPages.contains(1),
      ),
    ),
    PermissionWrapper(
      permissionKey: 'patients',
      featureName: 'المرضى',
      child: PatientsPage(
        isVisible: _currentIndex == 2,
        hasBeenVisited: _visitedPages.contains(2),
      ),
    ),
    PermissionWrapper(
      permissionKey: 'products',
      featureName: 'المنتجات',
      child: ProductsPage(
        isVisible: _currentIndex == 3,
        hasBeenVisited: _visitedPages.contains(3),
      ),
    ),
    PermissionWrapper(
      permissionKey: 'employees',
      featureName: 'الموظفين',
      child: EmployeesMainPage(
        isVisible: _currentIndex == 4,
        hasBeenVisited: _visitedPages.contains(4),
      ),
    ),
    const SettingsPage(),
  ];

  final List<BottomNavigationBarItem> _bottomNavItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.calendar_today),
      label: AppStrings.appointments,
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.point_of_sale),
      label: 'المبيعات',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.people),
      label: AppStrings.patients,
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.inventory),
      label: AppStrings.products,
    ),

    const BottomNavigationBarItem(
      icon: Icon(Icons.people_alt),
      label: 'الموظفين',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.settings),
      label: 'الإعدادات',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _currentIndex, children: _pages),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          boxShadow: [
            BoxShadow(
              color: AppColors.gray300.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
              _visitedPages.add(index); // Mark page as visited
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: AppColors.primary,
          unselectedItemColor: AppColors.gray500,
          selectedLabelStyle: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
          ),
          items: _bottomNavItems,
        ),
      ),
    );
  }
}
