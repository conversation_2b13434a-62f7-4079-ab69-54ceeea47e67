import 'package:equatable/equatable.dart';

enum ExpenseCategory { supplies, maintenance, utilities, services, other }

extension ExpenseCategoryExtension on ExpenseCategory {
  String get value {
    switch (this) {
      case ExpenseCategory.supplies:
        return 'supplies';
      case ExpenseCategory.maintenance:
        return 'maintenance';
      case ExpenseCategory.utilities:
        return 'utilities';
      case ExpenseCategory.services:
        return 'services';
      case ExpenseCategory.other:
        return 'other';
    }
  }

  String get arabicName {
    switch (this) {
      case ExpenseCategory.supplies:
        return 'مستلزمات';
      case ExpenseCategory.maintenance:
        return 'صيانة';
      case ExpenseCategory.utilities:
        return 'خدمات عامة';
      case ExpenseCategory.services:
        return 'خدمات';
      case ExpenseCategory.other:
        return 'أخرى';
    }
  }

  static ExpenseCategory fromString(String value) {
    switch (value) {
      case 'supplies':
        return ExpenseCategory.supplies;
      case 'maintenance':
        return ExpenseCategory.maintenance;
      case 'utilities':
        return ExpenseCategory.utilities;
      case 'services':
        return ExpenseCategory.services;
      case 'other':
        return ExpenseCategory.other;
      default:
        return ExpenseCategory.other;
    }
  }
}

class AdminModel extends Equatable {
  final String id;
  final String name;
  final String email;
  final String role;
  final String employeeType;

  const AdminModel({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    required this.employeeType,
  });

  factory AdminModel.fromJson(Map<String, dynamic> json) {
    return AdminModel(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? 'غير محدد',
      email: json['email'] as String? ?? '',
      role: json['role'] as String? ?? 'admin',
      employeeType: json['employee_type'] as String? ?? 'admin',
    );
  }

  @override
  List<Object?> get props => [id, name, email, role, employeeType];
}

class ExpenseModel extends Equatable {
  final String id;
  final String title;
  final String? description;
  final double amount;
  final ExpenseCategory category;
  final DateTime expenseDate;
  final String? receipt; // Path to receipt image
  final String createdBy;
  final AdminModel? admin; // Admin who created this expense
  final DateTime createdAt;
  final DateTime updatedAt;

  const ExpenseModel({
    required this.id,
    required this.title,
    this.description,
    required this.amount,
    required this.category,
    required this.expenseDate,
    this.receipt,
    required this.createdBy,
    this.admin,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ExpenseModel.fromJson(Map<String, dynamic> json) {
    return ExpenseModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      amount: (json['amount'] as num).toDouble(),
      category: ExpenseCategoryExtension.fromString(json['category'] as String),
      expenseDate: DateTime.parse(json['expense_date'] as String),
      receipt: json['receipt'] as String?,
      createdBy: json['created_by'] as String,
      admin: json['admins'] != null ? AdminModel.fromJson(json['admins']) : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'amount': amount,
      'category': category.value,
      'expense_date': expenseDate.toIso8601String(),
      'receipt': receipt,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ExpenseModel copyWith({
    String? id,
    String? title,
    String? description,
    double? amount,
    ExpenseCategory? category,
    DateTime? expenseDate,
    String? receipt,
    String? createdBy,
    AdminModel? admin,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExpenseModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      category: category ?? this.category,
      expenseDate: expenseDate ?? this.expenseDate,
      receipt: receipt ?? this.receipt,
      createdBy: createdBy ?? this.createdBy,
      admin: admin ?? this.admin,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        amount,
        category,
        expenseDate,
        receipt,
        createdBy,
        admin,
        createdAt,
        updatedAt,
      ];
}
