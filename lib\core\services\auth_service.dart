import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../network/supabase_client.dart';
import '../models/patient_model.dart';
import 'permissions_service.dart';

class AuthService {
  static final SupabaseClient _client = SupabaseConfig.client;
  
  // Get current user
  static User? get currentUser => _client.auth.currentUser;
  
  // Get current user ID (auth.uid)
  static String? get currentUserId => _client.auth.currentUser?.id;
  
  // Check if user is authenticated
  static bool get isAuthenticated => _client.auth.currentUser != null;
  
  // Sign in with email and password
  static Future<AuthResponse> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('🔐 AuthService: Attempting to sign in user: $email');
      
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      if (response.user != null) {
        debugPrint('✅ AuthService: Successfully signed in user: ${response.user!.email}');
      }
      
      return response;
    } catch (e) {
      debugPrint('❌ AuthService: Error signing in: $e');
      rethrow;
    }
  }
  
  // Sign up with email and password
  static Future<AuthResponse> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      debugPrint('🔐 AuthService: Attempting to sign up user: $email');
      
      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: {
          'name': name,
          ...?additionalData,
        },
      );
      
      if (response.user != null) {
        debugPrint('✅ AuthService: Successfully signed up user: ${response.user!.email}');
      }
      
      return response;
    } catch (e) {
      debugPrint('❌ AuthService: Error signing up: $e');
      rethrow;
    }
  }
  
  // Sign out
  static Future<void> signOut() async {
    try {
      debugPrint('🔐 AuthService: Signing out user');
      await _client.auth.signOut();
      debugPrint('✅ AuthService: Successfully signed out');
    } catch (e) {
      debugPrint('❌ AuthService: Error signing out: $e');
      rethrow;
    }
  }
  
  // Reset password
  static Future<void> resetPassword(String email) async {
    try {
      debugPrint('🔐 AuthService: Sending password reset email to: $email');
      await _client.auth.resetPasswordForEmail(email);
      debugPrint('✅ AuthService: Password reset email sent');
    } catch (e) {
      debugPrint('❌ AuthService: Error sending password reset email: $e');
      rethrow;
    }
  }
  
  // Update user profile
  static Future<UserResponse> updateProfile({
    String? email,
    String? password,
    Map<String, dynamic>? data,
  }) async {
    try {
      debugPrint('🔐 AuthService: Updating user profile');
      
      final response = await _client.auth.updateUser(
        UserAttributes(
          email: email,
          password: password,
          data: data,
        ),
      );
      
      debugPrint('✅ AuthService: Successfully updated user profile');
      return response;
    } catch (e) {
      debugPrint('❌ AuthService: Error updating user profile: $e');
      rethrow;
    }
  }
  
  // Get current patient profile
  static Future<PatientModel?> getCurrentPatientProfile() async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        debugPrint('⚠️ AuthService: No authenticated user');
        return null;
      }
      
      debugPrint('🔍 AuthService: Loading patient profile for user: $userId');
      
      final response = await SupabaseConfig.patients
          .select()
          .eq('id', userId)
          .single();
      
      final patient = PatientModel.fromJson(response);
      debugPrint('✅ AuthService: Successfully loaded patient profile');
      
      return patient;
    } catch (e) {
      debugPrint('❌ AuthService: Error loading patient profile: $e');
      return null;
    }
  }
  
  // Create patient profile (called automatically by trigger)
  static Future<PatientModel> createPatientProfile({
    required String name,
    String? email,
    String? phone,
    int? age,
    DateTime? birthDate,
    String? gender,
    double? height,
    double? weight,
  }) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('No authenticated user');
      }
      
      debugPrint('🔍 AuthService: Creating patient profile for user: $userId');
      
      final patientData = {
        'id': userId, // Use auth.uid as primary key
        'name': name,
        'email': email ?? currentUser?.email,
        'phone': phone,
        'age': age,
        'birth_date': birthDate?.toIso8601String().split('T')[0],
        'gender': gender,
        'height': height,
        'weight': weight,
        'is_premium': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };
      
      final response = await SupabaseConfig.patients
          .insert(patientData)
          .select()
          .single();
      
      final patient = PatientModel.fromJson(response);
      debugPrint('✅ AuthService: Successfully created patient profile');
      
      return patient;
    } catch (e) {
      debugPrint('❌ AuthService: Error creating patient profile: $e');
      rethrow;
    }
  }
  
  // Listen to auth state changes
  static Stream<AuthState> get authStateChanges => _client.auth.onAuthStateChange;
  
  // Check if user is admin
  static Future<bool> isAdmin() async {
    try {
      final userId = currentUserId;
      if (userId == null) return false;
      
      final response = await SupabaseConfig.admins
          .select('id')
          .eq('id', userId)
          .limit(1);
      
      return response.isNotEmpty;
    } catch (e) {
      debugPrint('❌ AuthService: Error checking admin status: $e');
      return false;
    }
  }
  
  // Get admin role
  static Future<String?> getAdminRole() async {
    try {
      final userId = currentUserId;
      if (userId == null) return null;

      final response = await SupabaseConfig.admins
          .select('role, employee_type')
          .eq('id', userId)
          .single();

      return response['employee_type'] as String? ?? response['role'] as String?;
    } catch (e) {
      debugPrint('❌ AuthService: Error getting admin role: $e');
      return null;
    }
  }

  // Get current employee info
  static Future<Map<String, dynamic>?> getCurrentEmployee() async {
    try {
      final userId = currentUserId;
      if (userId == null) return null;

      final response = await SupabaseConfig.admins
          .select('''
            *,
            specializations:specialization_id (
              id,
              name,
              name_en,
              description,
              color,
              icon
            )
          ''')
          .eq('id', userId)
          .single();

      return response;
    } catch (e) {
      debugPrint('❌ AuthService: Error getting current employee: $e');
      return null;
    }
  }

  // Check if current user has specific permission
  static Future<bool> hasPermission(String permission) async {
    try {
      final employeeType = await getAdminRole();
      if (employeeType == null) return false;

      return await PermissionsService.hasPermission(employeeType, permission);
    } catch (e) {
      debugPrint('❌ AuthService: Error checking permission: $e');
      return false;
    }
  }

  // Get current user permissions
  static Future<Map<String, bool>> getCurrentUserPermissions() async {
    try {
      final employeeType = await getAdminRole();
      if (employeeType == null) return {};

      return await PermissionsService.getPermissionsByEmployeeType(employeeType);
    } catch (e) {
      debugPrint('❌ AuthService: Error getting user permissions: $e');
      return {};
    }
  }
}
