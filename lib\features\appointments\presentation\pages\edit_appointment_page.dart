import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../appointments/data/repositories/appointments_repository.dart';
import '../../../appointments/data/repositories/time_slots_repository.dart';
import '../../../appointments/data/repositories/holidays_repository.dart';
import '../../../patients/data/repositories/patients_repository.dart';
import '../widgets/time_slot_selection_dialog.dart';
import 'print_appointment_invoice_page.dart';

class EditAppointmentPage extends StatefulWidget {
  final AppointmentModel appointment;

  const EditAppointmentPage({
    super.key,
    required this.appointment,
  });

  @override
  State<EditAppointmentPage> createState() => _EditAppointmentPageState();
}

class _EditAppointmentPageState extends State<EditAppointmentPage> {
  final AppointmentsRepository _appointmentsRepository = AppointmentsRepository();
  final TimeSlotsRepository _timeSlotsRepository = TimeSlotsRepository();
  final HolidaysRepository _holidaysRepository = HolidaysRepository();
  final PatientsRepository _patientsRepository = PatientsRepository();

  late TextEditingController _consultationFeeController;
  late TextEditingController _paidAmountController;
  late TextEditingController _notesController;
  late TextEditingController _groupPaymentController;

  DateTime? _selectedDate;
  TimeSlotModel? _selectedTimeSlot;
  List<TimeSlotModel> _availableTimeSlots = [];
  bool _isLoadingTimeSlots = false;
  String _selectedStatus = AppointmentModel.statusConfirmed;
  PatientModel? _patient;
  bool _isLoadingPatient = false;
  List<AppointmentModel>? _groupAppointments;
  bool _isLoadingGroup = false;
  double _totalGroupFee = 0.0;
  double _totalGroupPaid = 0.0;
  double _totalGroupRemaining = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadPatientData();
    if (widget.appointment.isMultipleBooking && widget.appointment.multipleBookingGroupId != null) {
      _loadGroupAppointments();
    }
  }

  void _initializeControllers() {
    _consultationFeeController = TextEditingController(
      text: widget.appointment.consultationFee.toString(),
    );
    _paidAmountController = TextEditingController(
      text: widget.appointment.paidAmount.toString(),
    );
    _notesController = TextEditingController(
      text: widget.appointment.notes ?? '',
    );
    _groupPaymentController = TextEditingController();
    _selectedDate = widget.appointment.appointmentDate;
    _selectedStatus = widget.appointment.status;
  }

  Future<void> _loadPatientData() async {
    if (widget.appointment.patientId == null) return;

    setState(() {
      _isLoadingPatient = true;
    });

    try {
      final patient = await _patientsRepository.getPatientById(widget.appointment.patientId!);
      setState(() {
        _patient = patient;
        _isLoadingPatient = false;
      });
      
      // Load time slots for the current date
      await _loadAvailableTimeSlots();
    } catch (e) {
      setState(() {
        _isLoadingPatient = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات المريض: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _loadGroupAppointments() async {
    if (_isLoadingGroup) return;

    setState(() {
      _isLoadingGroup = true;
    });

    try {
      final groupAppointments = await _appointmentsRepository.getAppointmentsByGroupId(
        widget.appointment.multipleBookingGroupId.toString(),
      );

      if (mounted) {
        setState(() {
          _groupAppointments = groupAppointments;
          _totalGroupFee = groupAppointments.fold(0.0, (sum, apt) => sum + apt.consultationFee);
          _totalGroupPaid = groupAppointments.fold(0.0, (sum, apt) => sum + apt.paidAmount);
          _totalGroupRemaining = groupAppointments.fold(0.0, (sum, apt) => sum + apt.remainingAmount);
          _isLoadingGroup = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingGroup = false;
        });
      }
      debugPrint('❌ Error loading group appointments: $e');
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // Check if the selected date is a holiday
      final isHoliday = await _checkIfHoliday(picked);
      if (isHoliday) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('التاريخ المحدد يوافق إجازة، يرجى اختيار تاريخ آخر'),
              backgroundColor: AppColors.error,
            ),
          );
        }
        return;
      }

      setState(() {
        _selectedDate = picked;
        _selectedTimeSlot = null;
        _availableTimeSlots = [];
      });
      
      await _loadAvailableTimeSlots();
    }
  }

  Future<bool> _checkIfHoliday(DateTime date) async {
    try {
      final holidays = await _holidaysRepository.getHolidaysByDate(date);
      return holidays.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking holidays: $e');
      return false;
    }
  }

  Future<void> _loadAvailableTimeSlots() async {
    if (_selectedDate == null) return;

    setState(() {
      _isLoadingTimeSlots = true;
    });

    try {
      final timeSlots = await _timeSlotsRepository.getAvailableTimeSlots(_selectedDate!);
      
      // Add current time slot to available slots if it's not already there
      if (widget.appointment.timeSlotId != null) {
        final currentTimeSlot = await _timeSlotsRepository.getTimeSlotById(widget.appointment.timeSlotId!);
        if (currentTimeSlot != null && !timeSlots.any((slot) => slot.id == currentTimeSlot.id)) {
          timeSlots.add(currentTimeSlot);
          timeSlots.sort((a, b) => a.startTime.compareTo(b.startTime));
        }
        _selectedTimeSlot = currentTimeSlot;
      } else if (widget.appointment.appointmentTime != null && timeSlots.isNotEmpty) {
        // Try to find time slot by appointment time if timeSlotId is null
        try {
          _selectedTimeSlot = timeSlots.firstWhere(
            (slot) => slot.startTime == widget.appointment.appointmentTime,
          );
        } catch (e) {
          // If not found, use the first available slot
          _selectedTimeSlot = timeSlots.first;
        }
      }
      
      setState(() {
        _availableTimeSlots = timeSlots;
        _isLoadingTimeSlots = false;
      });
    } catch (e) {
      setState(() {
        _availableTimeSlots = [];
        _isLoadingTimeSlots = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الأوقات المتاحة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _updateAppointment() async {
    debugPrint('🔄 _updateAppointment called');
    if (_selectedDate == null) {
      debugPrint('❌ Selected date is null');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى اختيار تاريخ الموعد'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }
    debugPrint('✅ Selected date: $_selectedDate');

    // Show loading dialog
    _showLoadingDialog('جاري تحديث الموعد...');

    // Ensure we have a time slot selected or use the original one
    if (_selectedTimeSlot == null && widget.appointment.timeSlotId != null) {
      try {
        _selectedTimeSlot = await _timeSlotsRepository.getTimeSlotById(widget.appointment.timeSlotId!);
        debugPrint('🔄 Loaded original time slot: ${_selectedTimeSlot?.startTime}');
      } catch (e) {
        debugPrint('❌ Error loading original time slot: $e');
      }
    }

    // Final check - if still null, try to get from appointment_time
    if (_selectedTimeSlot == null && widget.appointment.appointmentTime != null) {
      debugPrint('⚠️ TimeSlot is null, using appointment_time: ${widget.appointment.appointmentTime}');
    }

    final consultationFee = double.tryParse(_consultationFeeController.text) ?? 0.0;
    final paidAmount = double.tryParse(_paidAmountController.text) ?? 0.0;

    try {
      if (widget.appointment.isMultipleBooking && _groupAppointments != null) {
        // For multiple booking, we only update date/time/status, not payment
        // Payment is handled separately through the Pay button
        await _updateMultipleBookingGroup(_totalGroupFee, _totalGroupPaid);
      } else {
        // Update single appointment
        final updatedAppointment = widget.appointment.copyWith(
          appointmentDate: _selectedDate,
          timeSlotId: _selectedTimeSlot?.id ?? widget.appointment.timeSlotId,
          employeeId: _selectedTimeSlot?.employeeId ?? widget.appointment.employeeId, // Update employee_id to match time slot
          status: _selectedStatus,
          consultationFee: consultationFee,
          paidAmount: paidAmount,
          remainingAmount: consultationFee - paidAmount,
          notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
          updatedAt: DateTime.now(),
        );

        debugPrint('🔄 About to update single appointment...');
        await _appointmentsRepository.updateAppointment(updatedAppointment);
        debugPrint('✅ Single appointment updated successfully');

        if (mounted) {
          Navigator.of(context).pop(); // Close loading dialog
          _showSuccessDialog(updatedAppointment);
        }
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error updating single appointment: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الموعد: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _updateMultipleBookingGroup(double totalFee, double totalPaid) async {
    final groupSize = _groupAppointments!.length;
    final feePerAppointment = totalFee / groupSize;

    // For regular save, we don't add additional payment - just redistribute existing amounts
    final paidPerAppointment = totalPaid / groupSize;
    final remainingPerAppointment = feePerAppointment - paidPerAppointment;

    debugPrint('📊 Multiple booking group update (no additional payment):');
    debugPrint('   Total fee: $totalFee');
    debugPrint('   Total paid: $totalPaid');
    debugPrint('   Paid per appointment: $paidPerAppointment');
    debugPrint('   Remaining per appointment: $remainingPerAppointment');

    // Update all appointments in the group
    for (final appointment in _groupAppointments!) {
      final updatedAppointment = appointment.copyWith(
        consultationFee: feePerAppointment,
        paidAmount: paidPerAppointment,
        remainingAmount: remainingPerAppointment,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        updatedAt: DateTime.now(),
      );

      await _appointmentsRepository.updateAppointment(updatedAppointment);
    }

    // Update the current appointment with date/time/status changes
    final currentUpdatedAppointment = widget.appointment.copyWith(
      appointmentDate: _selectedDate,
      timeSlotId: _selectedTimeSlot?.id ?? widget.appointment.timeSlotId,
      employeeId: _selectedTimeSlot?.employeeId ?? widget.appointment.employeeId, // Update employee_id to match time slot
      status: _selectedStatus,
      consultationFee: feePerAppointment,
      paidAmount: paidPerAppointment,
      remainingAmount: remainingPerAppointment,
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      updatedAt: DateTime.now(),
    );

    try {
      debugPrint('🔄 About to update multiple booking group...');
      await _appointmentsRepository.updateAppointment(currentUpdatedAppointment);
      debugPrint('✅ Multiple booking group updated successfully');

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        _showSuccessDialog(currentUpdatedAppointment);
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error updating multiple booking group: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الحجز المتعدد: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Text(
                message,
                style: TextStyle(fontSize: 14.sp),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSuccessDialog(AppointmentModel appointment) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.success, size: 24.sp),
            SizedBox(width: 8.w),
            Text(
              'تم التحديث بنجاح',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
          ],
        ),
        content: Text(
          'تم تحديث الموعد بنجاح. هل تريد طباعة فاتورة الحجز المحدثة؟',
          style: TextStyle(fontSize: 14.sp),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(appointment); // Go back with updated appointment
            },
            child: Text(
              'لا، شكراً',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              final navigator = Navigator.of(context);
              navigator.pop(); // Close dialog
              if (_patient != null) {
                // Navigate to print page and wait for it to complete
                await navigator.push(
                  MaterialPageRoute(
                    builder: (context) => PrintAppointmentInvoicePage(
                      appointment: appointment,
                      patient: _patient!,
                    ),
                  ),
                );
                // After printing, go back to appointments list with updated data
                if (mounted) {
                  navigator.pop(appointment);
                }
              } else {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ: لا توجد بيانات المريض'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            icon: Icon(Icons.print, size: 16.sp, color: AppColors.white),
            label: Text(
              'طباعة الفاتورة',
              style: TextStyle(color: AppColors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  String _getDayName(DateTime date) {
    final dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    return dayNames[date.weekday % 7];
  }

  Future<void> _processGroupPayment() async {
    if (_groupAppointments == null || _groupAppointments!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('لا توجد مواعيد في المجموعة'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // Show loading dialog
    _showLoadingDialog('جاري معالجة الدفع...');

    final additionalPayment = double.tryParse(_groupPaymentController.text) ?? 0.0;

    if (additionalPayment <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('أدخل مبلغ صحيح'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    if (additionalPayment > _totalGroupRemaining) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('المبلغ يتجاوز المبلغ المتبقي'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    try {
      // Distribute payment across all appointments in the group
      final groupSize = _groupAppointments!.length;
      final paymentPerAppointment = additionalPayment / groupSize;

      debugPrint('🔄 ProcessGroupPayment: additionalPayment=$additionalPayment, groupSize=$groupSize, paymentPerAppointment=$paymentPerAppointment');

      for (final appointment in _groupAppointments!) {
        final newPaidAmount = appointment.paidAmount + paymentPerAppointment;
        final newRemainingAmount = appointment.consultationFee - newPaidAmount;

        debugPrint('🔄 Updating appointment ${appointment.id}: oldPaid=${appointment.paidAmount}, newPaid=$newPaidAmount, newRemaining=$newRemainingAmount');

        final updatedAppointment = appointment.copyWith(
          paidAmount: newPaidAmount,
          remainingAmount: newRemainingAmount,
          updatedAt: DateTime.now(),
        );

        debugPrint('🔄 About to update appointment ${appointment.id}...');
        await _appointmentsRepository.updateAppointment(updatedAppointment);
        debugPrint('✅ Updated appointment ${appointment.id} successfully');
      }

      // Clear the payment field
      _groupPaymentController.clear();
      debugPrint('🔄 Cleared payment field');

      // Reload group data
      debugPrint('🔄 Reloading group data...');
      await _loadGroupAppointments();
      debugPrint('✅ Group data reloaded successfully');

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم دفع المبلغ بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error in _processGroupPayment: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في معالجة الدفع: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _showTimeSlotSelectionDialog() async {
    if (_selectedDate == null || _availableTimeSlots.isEmpty) return;

    final selectedTimeSlot = await showDialog<TimeSlotModel>(
      context: context,
      builder: (context) => TimeSlotSelectionDialog(
        availableTimeSlots: _availableTimeSlots,
        selectedTimeSlot: _selectedTimeSlot,
        selectedDate: _selectedDate!,
      ),
    );

    if (selectedTimeSlot != null) {
      setState(() {
        _selectedTimeSlot = selectedTimeSlot;
      });
    }
  }

  @override
  void dispose() {
    _consultationFeeController.dispose();
    _paidAmountController.dispose();
    _notesController.dispose();
    _groupPaymentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تعديل الموعد',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: IconThemeData(color: AppColors.white),
        actions: [
          TextButton(
            onPressed: () {
              debugPrint('🔘 Save button pressed');
              _updateAppointment();
            },
            child: Text(
              'حفظ',
              style: TextStyle(
                color: AppColors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: _isLoadingPatient
          ? Center(
              child: CircularProgressIndicator(color: AppColors.primary),
            )
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Patient Info Card
                  if (_patient != null) ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12.r),
                        border: Border.all(color: AppColors.primary),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'بيانات المريض',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'الاسم: ${_patient!.name}',
                            style: TextStyle(fontSize: 14.sp),
                          ),
                          Text(
                            'رقم المريض: ${_patient!.patientId ?? 'غير محدد'}',
                            style: TextStyle(fontSize: 14.sp),
                          ),
                          if (_patient!.phone != null)
                            Text(
                              'الهاتف: ${_patient!.phone}',
                              style: TextStyle(fontSize: 14.sp),
                            ),
                        ],
                      ),
                    ),
                    SizedBox(height: 20.h),
                  ],

                  // Status Selection
                  _buildSectionTitle('حالة الموعد'),
                  SizedBox(height: 8.h),
                  DropdownButtonFormField<String>(
                    value: _selectedStatus,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    items: [
                      DropdownMenuItem(
                        value: AppointmentModel.statusConfirmed,
                        child: Text('مؤكد'),
                      ),
                      DropdownMenuItem(
                        value: AppointmentModel.statusCompleted,
                        child: Text('مكتمل'),
                      ),
                      DropdownMenuItem(
                        value: AppointmentModel.statusCancelled,
                        child: Text('ملغي'),
                      ),
                      DropdownMenuItem(
                        value: AppointmentModel.statusNoShow,
                        child: Text('لم يحضر'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedStatus = value!;
                      });
                    },
                  ),

                  SizedBox(height: 20.h),

                  // Date Selection
                  _buildSectionTitle('تاريخ الموعد'),
                  SizedBox(height: 8.h),
                  InkWell(
                    onTap: _selectDate,
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.primary),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.calendar_today, color: AppColors.primary),
                          SizedBox(width: 12.w),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _selectedDate == null
                                    ? 'اختر التاريخ'
                                    : '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: _selectedDate == null
                                      ? AppColors.textSecondary
                                      : AppColors.textPrimary,
                                ),
                              ),
                              if (_selectedDate != null) ...[
                                SizedBox(height: 4.h),
                                Text(
                                  _getDayName(_selectedDate!),
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 20.h),

                  // Time Slots Selection
                  if (_selectedDate != null) ...[
                    _buildSectionTitle('وقت الموعد'),
                    SizedBox(height: 8.h),
                    if (_isLoadingTimeSlots)
                      Center(
                        child: CircularProgressIndicator(color: AppColors.primary),
                      )
                    else if (_availableTimeSlots.isEmpty)
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(20.w),
                        decoration: BoxDecoration(
                          color: AppColors.error.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          'لا توجد أوقات متاحة في هذا التاريخ',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      )
                    else
                      InkWell(
                        onTap: _showTimeSlotSelectionDialog,
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.primary),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.access_time, color: AppColors.primary),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _selectedTimeSlot == null
                                          ? 'اختر الوقت'
                                          : '${_selectedTimeSlot!.startTime} - ${_selectedTimeSlot!.endTime}',
                                      style: TextStyle(
                                        fontSize: 16.sp,
                                        color: _selectedTimeSlot == null
                                            ? AppColors.textSecondary
                                            : AppColors.textPrimary,
                                        fontWeight: _selectedTimeSlot == null
                                            ? FontWeight.normal
                                            : FontWeight.w600,
                                      ),
                                    ),
                                    if (_selectedTimeSlot?.employeeName != null) ...[
                                      SizedBox(height: 4.h),
                                      Text(
                                        'د. ${_selectedTimeSlot!.employeeName}',
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          color: AppColors.primary,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                    if (_selectedTimeSlot?.specialization != null) ...[
                                      SizedBox(height: 2.h),
                                      Text(
                                        _selectedTimeSlot!.specialization!,
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: AppColors.textSecondary,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                              Icon(Icons.arrow_drop_down, color: AppColors.primary),
                            ],
                          ),
                        ),
                      ),

                    SizedBox(height: 20.h),
                  ],

                  // Multiple Booking Info
                  if (widget.appointment.isMultipleBooking) ...[
                    _buildMultipleBookingInfo(),
                    SizedBox(height: 20.h),
                  ],



                  // Notes
                  _buildSectionTitle('ملاحظات'),
                  SizedBox(height: 8.h),
                  TextFormField(
                    controller: _notesController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      hintText: 'أدخل ملاحظات إضافية...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ),

                  SizedBox(height: 30.h),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  Widget _buildMultipleBookingInfo() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.group_work_outlined,
                color: AppColors.primary,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'معلومات الحجز المتعدد',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          if (_isLoadingGroup) ...[
            Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          ] else if (_groupAppointments != null) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'عدد الجلسات:',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  '${_groupAppointments!.length} جلسة',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),

            SizedBox(height: 8.h),

            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'إجمالي الرسوم:',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        '${_totalGroupFee.toStringAsFixed(2)} د.ا',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'المبلغ المدفوع:',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        '${_totalGroupPaid.toStringAsFixed(2)} د.ا',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.success,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'المبلغ المتبقي:',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        '${_totalGroupRemaining.toStringAsFixed(2)} د.ا',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.error,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Payment field for remaining amount
            if (_totalGroupRemaining > 0) ...[
              SizedBox(height: 12.h),
              TextFormField(
                controller: _groupPaymentController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'المبلغ المدفوع',
                  suffixText: 'د.ا',
                  hintText: 'أدخل المبلغ (الحد الأقصى: ${_totalGroupRemaining.toStringAsFixed(2)})',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) return null;
                  final amount = double.tryParse(value);
                  if (amount == null) return 'أدخل مبلغ صحيح';
                  if (amount > _totalGroupRemaining) return 'المبلغ يتجاوز المتبقي';
                  if (amount < 0) return 'المبلغ لا يمكن أن يكون سالب';
                  return null;
                },
              ),
              SizedBox(height: 12.h),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    debugPrint('🔘 Pay button pressed');
                    _processGroupPayment();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: Text(
                    'دفع',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ],
      ),
    );
  }
}
