import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/sales_invoice_model.dart';
import '../bloc/sales_bloc.dart';
import '../bloc/sales_event.dart';
import '../bloc/sales_state.dart';
import 'edit_invoice_page.dart';

class InvoicesListPage extends StatefulWidget {
  final bool isVisible;
  final bool hasBeenVisited;

  const InvoicesListPage({
    super.key,
    required this.isVisible,
    required this.hasBeenVisited,
  });

  @override
  State<InvoicesListPage> createState() => _InvoicesListPageState();
}

class _InvoicesListPageState extends State<InvoicesListPage> {
  final _searchController = TextEditingController();
  final _scrollController = ScrollController();
  PaymentStatus? _selectedPaymentStatus;
  DateTime? _selectedDate;



  @override
  void initState() {
    super.initState();
    if (widget.isVisible || widget.hasBeenVisited) {
      context.read<SalesBloc>().add(LoadInvoices());
    }
    
    _scrollController.addListener(_onScroll);
  }

  Color _getPaymentStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return Colors.green;
      case PaymentStatus.partial:
        return Colors.orange;
      case PaymentStatus.returned:
        return Colors.red;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<SalesBloc>().add(LoadMoreInvoices());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible && !widget.hasBeenVisited) {
      return const SizedBox.shrink();
    }

    return Scaffold(
      body: Column(
        children: [
          // Search and filters
          _buildSearchAndFilters(),
          
          // Invoices list
          Expanded(
            child: BlocBuilder<SalesBloc, SalesState>(
              builder: (context, state) {
                if (state is SalesLoading && state is! SalesLoaded) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state is SalesError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64.sp,
                          color: AppColors.error,
                        ),
                        SizedBox(height: 16.h),
                        Text(
                          state.message,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 16.h),
                        ElevatedButton(
                          onPressed: () {
                            context.read<SalesBloc>().add(LoadInvoices());
                          },
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                if (state is SalesLoaded) {
                  if (state.invoices.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.receipt_long_outlined,
                            size: 64.sp,
                            color: AppColors.textSecondary,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            'لا توجد فواتير',
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textSecondary,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'ابدأ بإنشاء فاتورة جديدة',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      context.read<SalesBloc>().add(RefreshInvoices());
                    },
                    child: ListView.builder(
                      controller: _scrollController,
                      padding: EdgeInsets.all(16.w),
                      itemCount: state.hasReachedMax
                          ? state.invoices.length
                          : state.invoices.length + 1,
                      itemBuilder: (context, index) {
                        if (index >= state.invoices.length) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }

                        final invoice = state.invoices[index];
                        return _buildInvoiceCard(invoice);
                      },
                    ),
                  );
                }

                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search field
          TextFormField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث برقم الفاتورة...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            onChanged: (query) {
              context.read<SalesBloc>().add(SearchInvoices(query));
            },
          ),
          
          SizedBox(height: 12.h),
          
          // Filter chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  'الكل',
                  null,
                  null,
                ),
                SizedBox(width: 8.w),
                _buildFilterChip(
                  'مدفوع جزئياً',
                  PaymentStatus.partial,
                  null,
                ),
                SizedBox(width: 8.w),
                _buildFilterChip(
                  'مدفوع بالكامل',
                  PaymentStatus.paid,
                  null,
                ),
                SizedBox(width: 8.w),
                _buildFilterChip(
                  'مرتجع',
                  PaymentStatus.returned,
                  null,
                ),
                SizedBox(width: 8.w),
                _buildDateFilterChip(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    PaymentStatus? paymentStatus,
    InvoiceStatus? invoiceStatus,
  ) {
    return BlocBuilder<SalesBloc, SalesState>(
      builder: (context, state) {
        bool isSelected = false;
        
        if (state is SalesLoaded) {
          if (paymentStatus == null && invoiceStatus == null) {
            isSelected = state.paymentStatusFilter == null && state.statusFilter == null;
          } else if (paymentStatus != null) {
            isSelected = state.paymentStatusFilter == paymentStatus;
          } else if (invoiceStatus != null) {
            isSelected = state.statusFilter == invoiceStatus;
          }
        }

        return FilterChip(
          label: Text(label),
          selected: isSelected,
          onSelected: (selected) {
            if (paymentStatus == null && invoiceStatus == null) {
              context.read<SalesBloc>().add(ClearFilters());
            } else if (paymentStatus != null) {
              context.read<SalesBloc>().add(
                FilterInvoicesByPaymentStatus(selected ? paymentStatus : null),
              );
            } else if (invoiceStatus != null) {
              context.read<SalesBloc>().add(
                FilterInvoicesByStatus(selected ? invoiceStatus : null),
              );
            }
          },
        );
      },
    );
  }

  Widget _buildDateFilterChip() {
    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.calendar_today,
            size: 16.sp,
            color: _selectedDate != null ? Colors.white : AppColors.textPrimary,
          ),
          SizedBox(width: 4.w),
          Text(
            _selectedDate != null
                ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                : 'التاريخ',
            style: TextStyle(
              fontSize: 12.sp,
              color: _selectedDate != null ? Colors.white : AppColors.textPrimary,
            ),
          ),
        ],
      ),
      selected: _selectedDate != null,
      onSelected: (selected) async {
        if (selected) {
          final date = await showDatePicker(
            context: context,
            initialDate: DateTime.now(),
            firstDate: DateTime(2020),
            lastDate: DateTime.now(),
          );
          if (date != null) {
            setState(() {
              _selectedDate = date;
            });
            _applyDateFilter();
          }
        } else {
          setState(() {
            _selectedDate = null;
          });
          _applyDateFilter();
        }
      },
      selectedColor: AppColors.primary,
      backgroundColor: AppColors.surface,
      checkmarkColor: Colors.white,
    );
  }

  void _applyDateFilter() {
    context.read<SalesBloc>().add(
      LoadInvoices(
        searchQuery: _searchController.text.trim().isEmpty ? null : _searchController.text.trim(),
        paymentStatus: _selectedPaymentStatus,
        dateFilter: _selectedDate,
      ),
    );
  }

  Widget _buildInvoiceCard(SalesInvoiceModel invoice) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: _getPaymentStatusColor(invoice.paymentStatus).withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: _getPaymentStatusColor(invoice.paymentStatus).withValues(alpha: 0.08),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'فاتورة #${invoice.invoiceNumber}',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        invoice.patient?.name ?? 'غير محدد',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(invoice.paymentStatus).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Text(
                        invoice.paymentStatus.arabicName,
                        style: TextStyle(
                          fontSize: 11.sp,
                          fontWeight: FontWeight.w600,
                          color: _getStatusColor(invoice.paymentStatus),
                        ),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '${invoice.finalAmount.toStringAsFixed(0)} د.ا',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            SizedBox(height: 12.h),
            
            // Details
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    Icons.calendar_today,
                    'التاريخ',
                    '${invoice.createdAt.day}/${invoice.createdAt.month}/${invoice.createdAt.year}\n${invoice.createdAt.hour.toString().padLeft(2, '0')}:${invoice.createdAt.minute.toString().padLeft(2, '0')}',
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    Icons.payment,
                    'نوع الدفع',
                    invoice.paymentType.arabicName,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    Icons.shopping_cart,
                    'المنتجات',
                    '${invoice.items.length}',
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    Icons.person,
                    'الأخصائي',
                    invoice.specialist?.name ?? 'غير محدد',
                  ),
                ),
              ],
            ),
            
            // Payment details based on status
            if (invoice.status == InvoiceStatus.returned) ...[
              // For returned invoices - show return summary like invoice summary
              SizedBox(height: 8.h),
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      Icons.money_off,
                      'المبلغ المرتجع',
                      '${invoice.returnAmount.toStringAsFixed(0)} د.ا',
                      color: Colors.red,
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      Icons.account_balance,
                      'المبلغ المستحق',
                      '${_calculateDueAmount(invoice).toStringAsFixed(0)} د.ا',
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ] else if (invoice.paymentType == PaymentType.installment) ...[
              // For installment invoices (not returned)
              SizedBox(height: 8.h),
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      Icons.account_balance_wallet,
                      'المدفوع',
                      '${invoice.paidAmount.toStringAsFixed(0)} د.ا',
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      Icons.schedule,
                      'المتبقي',
                      '${invoice.remainingAmount.toStringAsFixed(0)} د.ا',
                    ),
                  ),
                  if (invoice.dueDate != null)
                    Expanded(
                      child: _buildDetailItem(
                        Icons.event,
                        'الاستحقاق',
                        '${invoice.dueDate!.day}/${invoice.dueDate!.month}',
                      ),
                    ),
                ],
              ),
            ],

            // Action buttons
            SizedBox(height: 12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => EditInvoicePage(invoice: invoice),
                      ),
                    );
                  },
                  icon: Icon(
                    Icons.edit,
                    size: 16.sp,
                    color: AppColors.primary,
                  ),
                  label: Text(
                    'تعديل',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(IconData icon, String label, String value, {Color? color}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: color ?? AppColors.textSecondary,
        ),
        SizedBox(width: 4.w),
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 10.sp,
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              value,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                color: color ?? AppColors.textPrimary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.partial:
        return Colors.blue;
      case PaymentStatus.paid:
        return Colors.green;
      case PaymentStatus.returned:
        return Colors.red;
    }
  }

  /// Calculate due amount for returned invoices (same logic as InvoiceSummaryWidget)
  double _calculateDueAmount(SalesInvoiceModel invoice) {
    // Calculate subtotal from items
    final subtotal = invoice.items.fold<double>(
      0.0,
      (sum, item) => sum + item.finalPrice,
    );

    // Calculate discount amount
    final discountAmount = subtotal * (invoice.discountPercentage / 100);

    // Calculate amount after discount
    final afterDiscount = subtotal - discountAmount;

    // For returned invoices: due amount = after discount - return amount
    if (invoice.status == InvoiceStatus.returned) {
      return afterDiscount - invoice.returnAmount;
    }

    // For normal invoices: due amount = after discount
    return afterDiscount;
  }
}
