import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  static const String supabaseUrl = 'https://xqvdkdjnrcytswvfrkog.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.zaHSYMMK1QIRwCckgZXhT287rdW2IQUbY5Ag4U7PiRg';

  static Future<void> initialize() async {
    await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);
  }

  static SupabaseClient get client => Supabase.instance.client;
  static GoTrueClient get auth => client.auth;
  static SupabaseQueryBuilder get admins => client.from('admins');
  static SupabaseQueryBuilder get patients => client.from('patients');
  static SupabaseQueryBuilder get appointments => client.from('appointments');
  static SupabaseQueryBuilder get timeSlots => client.from('time_slots');
  static SupabaseQueryBuilder get weeklyResults =>
      client.from('weekly_results');
  static SupabaseQueryBuilder get labTests => client.from('lab_tests');
  static SupabaseQueryBuilder get products => client.from('products');
  static SupabaseQueryBuilder get articles => client.from('articles');
  static SupabaseQueryBuilder get categories => client.from('categories');
  static SupabaseQueryBuilder get holidays => client.from('holidays');
  static SupabaseQueryBuilder get clinicInfo => client.from('clinic_info');
  static SupabaseQueryBuilder get reminders => client.from('reminders');
  static SupabaseQueryBuilder get medicalInfo => client.from('medical_info');
  static SupabaseQueryBuilder get salesInvoices => client.from('sales_invoices');
  static SupabaseQueryBuilder get invoiceItems => client.from('invoice_items');
  static SupabaseQueryBuilder get payments => client.from('payments');
  static SupabaseQueryBuilder get returns => client.from('returns');
  static SupabaseQueryBuilder get returnItems => client.from('return_items');

  // Storage references
  static SupabaseStorageClient get storage => client.storage;

  // Storage bucket helpers
  static get productImagesBucket => storage.from('product-images');
  static get articleImagesBucket => storage.from('article-images');
  static get articlePdfsBucket => storage.from('article-pdfs');
  static get labTestImagesBucket => storage.from('lab-test-images');

  // Test storage connectivity
}
