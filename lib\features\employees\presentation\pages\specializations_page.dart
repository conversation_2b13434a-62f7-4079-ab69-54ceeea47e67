import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/specialization_model.dart';
import '../../data/repositories/specializations_repository.dart';
import '../widgets/add_specialization_dialog.dart';

class SpecializationsPage extends StatefulWidget {
  const SpecializationsPage({super.key});

  @override
  State<SpecializationsPage> createState() => _SpecializationsPageState();
}

class _SpecializationsPageState extends State<SpecializationsPage> {
  List<SpecializationModel> _specializations = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSpecializations();
  }

  Future<void> _loadSpecializations() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final specializations = await SpecializationsRepository.getAllSpecializations();
      setState(() {
        _specializations = specializations;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحميل التخصصات: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(24),
            decoration: const BoxDecoration(
              color: AppColors.surface,
              border: Border(
                bottom: BorderSide(color: AppColors.border, width: 1),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.medical_services,
                  size: 32,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إدارة التخصصات',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'إدارة تخصصات الأخصائيين في المركز',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () async {
                    final result = await showDialog<bool>(
                      context: context,
                      builder: (context) => const AddSpecializationDialog(),
                    );
                    if (result == true) {
                      _loadSpecializations();
                    }
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة تخصص'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(color: AppColors.primary),
                  )
                : _specializations.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.medical_services_outlined,
                              size: 64,
                              color: AppColors.textSecondary,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'لا يوجد تخصصات',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'ابدأ بإضافة تخصص جديد',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(24),
                        itemCount: _specializations.length,
                        itemBuilder: (context, index) {
                          final specialization = _specializations[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: _buildSpecializationCard(specialization),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecializationCard(SpecializationModel specialization) {
    return Card(
      elevation: 2,
      color: AppColors.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Color Indicator
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Color(int.parse(specialization.color.replaceFirst('#', '0xFF'))),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                _getIconData(specialization.icon),
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),

            // Specialization Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    specialization.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  if (specialization.nameEn != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      specialization.nameEn!,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                  if (specialization.description != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      specialization.description!,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: specialization.isActive 
                              ? Colors.green.withValues(alpha: 0.1)
                              : Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          specialization.isActive ? 'نشط' : 'غير نشط',
                          style: TextStyle(
                            fontSize: 12,
                            color: specialization.isActive ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'ترتيب: ${specialization.displayOrder}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Actions
            Column(
              children: [
                IconButton(
                  onPressed: () async {
                    final result = await showDialog<bool>(
                      context: context,
                      builder: (context) => AddSpecializationDialog(
                        specialization: specialization,
                      ),
                    );
                    if (result == true) {
                      _loadSpecializations();
                    }
                  },
                  icon: const Icon(Icons.edit, color: AppColors.primary),
                  tooltip: 'تعديل',
                ),
                IconButton(
                  onPressed: () async {
                    try {
                      await SpecializationsRepository.toggleSpecializationStatus(
                        specialization.id,
                        !specialization.isActive,
                      );
                      _loadSpecializations();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            specialization.isActive 
                                ? 'تم إلغاء تفعيل ${specialization.name}'
                                : 'تم تفعيل ${specialization.name}',
                          ),
                        ),
                      );
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('خطأ: $e')),
                      );
                    }
                  },
                  icon: Icon(
                    specialization.isActive ? Icons.pause : Icons.play_arrow,
                    color: specialization.isActive ? Colors.orange : Colors.green,
                  ),
                  tooltip: specialization.isActive ? 'إلغاء التفعيل' : 'تفعيل',
                ),
                IconButton(
                  onPressed: () => _showDeleteConfirmation(specialization),
                  icon: const Icon(Icons.delete, color: AppColors.error),
                  tooltip: 'حذف',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconData(String? iconName) {
    switch (iconName) {
      case 'hearing':
        return Icons.hearing;
      case 'record_voice_over':
        return Icons.record_voice_over;
      case 'psychology':
        return Icons.psychology;
      case 'chat':
        return Icons.chat;
      case 'school':
        return Icons.school;
      default:
        return Icons.medical_services;
    }
  }

  void _showDeleteConfirmation(SpecializationModel specialization) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف التخصص "${specialization.name}"؟'),
            const SizedBox(height: 8),
            const Text(
              'تحذير: سيتم حذف التخصص نهائياً ولن يمكن استرداده.',
              style: TextStyle(
                color: AppColors.error,
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await SpecializationsRepository.deleteSpecialization(specialization.id);
                _loadSpecializations();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('تم حذف ${specialization.name} بنجاح')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('خطأ في الحذف: $e')),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
