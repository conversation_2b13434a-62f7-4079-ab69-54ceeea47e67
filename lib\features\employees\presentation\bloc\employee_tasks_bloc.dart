import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/employee_tasks_repository.dart';
import 'employee_tasks_event.dart';
import 'employee_tasks_state.dart';

class EmployeeTasksBloc extends Bloc<EmployeeTasksEvent, EmployeeTasksState> {
  EmployeeTasksBloc() : super(const EmployeeTasksInitial()) {
    on<LoadAllTasks>(_onLoadAllTasks);
    on<LoadTasksByEmployee>(_onLoadTasksByEmployee);
    on<LoadTasksByStatus>(_onLoadTasksByStatus);
    on<LoadOverdueTasks>(_onLoadOverdueTasks);
    on<LoadTasksDueSoon>(_onLoadTasksDueSoon);
    on<AddTask>(_onAddTask);
    on<UpdateTask>(_onUpdateTask);
    on<UpdateTaskStatus>(_onUpdateTaskStatus);
    on<DeleteTask>(_onDeleteTask);
    on<RefreshTasks>(_onRefreshTasks);
  }

  Future<void> _onLoadAllTasks(
    LoadAllTasks event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    emit(const EmployeeTasksLoading());
    try {
      debugPrint('🔄 EmployeeTasksBloc: Loading all tasks...');
      final tasks = await EmployeeTasksRepository.getAllTasks();
      debugPrint('✅ EmployeeTasksBloc: Successfully loaded ${tasks.length} tasks');
      emit(EmployeeTasksLoaded(tasks));
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error loading tasks: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onLoadTasksByEmployee(
    LoadTasksByEmployee event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    emit(const EmployeeTasksLoading());
    try {
      debugPrint('🔄 EmployeeTasksBloc: Loading tasks for employee: ${event.employeeId}');
      final tasks = await EmployeeTasksRepository.getTasksByEmployeeId(event.employeeId);
      debugPrint('✅ EmployeeTasksBloc: Successfully loaded ${tasks.length} tasks for employee');
      emit(EmployeeTasksLoaded(tasks));
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error loading employee tasks: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onLoadTasksByStatus(
    LoadTasksByStatus event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    emit(const EmployeeTasksLoading());
    try {
      debugPrint('🔄 EmployeeTasksBloc: Loading tasks with status: ${event.status}');
      final tasks = await EmployeeTasksRepository.getTasksByStatus(event.status);
      debugPrint('✅ EmployeeTasksBloc: Successfully loaded ${tasks.length} tasks with status ${event.status}');
      emit(EmployeeTasksLoaded(tasks));
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error loading tasks by status: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onLoadOverdueTasks(
    LoadOverdueTasks event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    emit(const EmployeeTasksLoading());
    try {
      debugPrint('🔄 EmployeeTasksBloc: Loading overdue tasks...');
      final tasks = await EmployeeTasksRepository.getOverdueTasks();
      debugPrint('✅ EmployeeTasksBloc: Successfully loaded ${tasks.length} overdue tasks');
      emit(EmployeeTasksLoaded(tasks));
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error loading overdue tasks: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onLoadTasksDueSoon(
    LoadTasksDueSoon event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    emit(const EmployeeTasksLoading());
    try {
      debugPrint('🔄 EmployeeTasksBloc: Loading tasks due soon...');
      final tasks = await EmployeeTasksRepository.getTasksDueSoon();
      debugPrint('✅ EmployeeTasksBloc: Successfully loaded ${tasks.length} tasks due soon');
      emit(EmployeeTasksLoaded(tasks));
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error loading tasks due soon: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onAddTask(
    AddTask event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    try {
      debugPrint('🔄 EmployeeTasksBloc: Adding new task: ${event.task.title}');
      final newTask = await EmployeeTasksRepository.addTask(event.task);
      debugPrint('✅ EmployeeTasksBloc: Successfully added task');
      
      emit(TaskAdded(newTask));
      
      // Reload all tasks
      add(const LoadAllTasks());
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error adding task: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onUpdateTask(
    UpdateTask event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    try {
      debugPrint('🔄 EmployeeTasksBloc: Updating task: ${event.task.title}');
      final updatedTask = await EmployeeTasksRepository.updateTask(event.task);
      debugPrint('✅ EmployeeTasksBloc: Successfully updated task');
      
      emit(TaskUpdated(updatedTask));
      
      // Reload all tasks
      add(const LoadAllTasks());
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error updating task: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onUpdateTaskStatus(
    UpdateTaskStatus event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    try {
      debugPrint('🔄 EmployeeTasksBloc: Updating task status: ${event.taskId} to ${event.status}');
      final updatedTask = await EmployeeTasksRepository.updateTaskStatus(event.taskId, event.status);
      debugPrint('✅ EmployeeTasksBloc: Successfully updated task status');
      
      emit(TaskStatusUpdated(updatedTask));
      
      // Reload all tasks
      add(const LoadAllTasks());
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error updating task status: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onDeleteTask(
    DeleteTask event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    try {
      debugPrint('🔄 EmployeeTasksBloc: Deleting task: ${event.taskId}');
      await EmployeeTasksRepository.deleteTask(event.taskId);
      debugPrint('✅ EmployeeTasksBloc: Successfully deleted task');
      
      emit(TaskDeleted(event.taskId));
      
      // Reload all tasks
      add(const LoadAllTasks());
    } catch (e) {
      debugPrint('❌ EmployeeTasksBloc Error deleting task: $e');
      emit(EmployeeTasksError(e.toString()));
    }
  }

  Future<void> _onRefreshTasks(
    RefreshTasks event,
    Emitter<EmployeeTasksState> emit,
  ) async {
    add(const LoadAllTasks());
  }
}
