-- Update appointments table to support enhanced booking system
-- Add new columns for payment tracking and multiple bookings

-- Add consultation fee column
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS consultation_fee DECIMAL(10,2) DEFAULT 0.0;

-- Add paid amount column
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS paid_amount DECIMAL(10,2) DEFAULT 0.0;

-- Add remaining amount column
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS remaining_amount DECIMAL(10,2) DEFAULT 0.0;

-- Add multiple booking flag
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS is_multiple_booking BOOLEAN DEFAULT FALSE;

-- Add multiple booking group ID
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS multiple_booking_group_id TEXT;

-- Add booking sequence for multiple bookings
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS booking_sequence INTEGER;

-- Update status column to support new statuses
-- Existing statuses: available, booked, confirmed, completed, cancelled
-- New status: no_show
-- No need to alter column as it's already TEXT

-- Add comments to clarify status meanings
COMMENT ON COLUMN appointments.status IS 'Appointment status: available, confirmed, completed, cancelled, no_show';

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_appointments_multiple_booking_group ON appointments(multiple_booking_group_id);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
CREATE INDEX IF NOT EXISTS idx_appointments_date_status ON appointments(appointment_date, status);

-- Check the updated structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'appointments'
ORDER BY ordinal_position;

-- Show current appointments count
SELECT COUNT(*) as total_appointments FROM appointments;

-- Show appointments by status
SELECT status, COUNT(*) as count
FROM appointments
GROUP BY status
ORDER BY count DESC;
