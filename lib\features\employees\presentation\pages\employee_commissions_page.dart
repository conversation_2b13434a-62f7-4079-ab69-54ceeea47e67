import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/admin_model.dart';
import '../../../../core/models/sales_invoice_model.dart';
import '../../../sales/data/repositories/sales_repository.dart';
import '../bloc/employees_bloc.dart';

class EmployeeCommissionsPage extends StatefulWidget {
  const EmployeeCommissionsPage({super.key});

  @override
  State<EmployeeCommissionsPage> createState() => _EmployeeCommissionsPageState();
}

class _EmployeeCommissionsPageState extends State<EmployeeCommissionsPage> {
  EmployeeModel? _selectedEmployee;
  List<SalesInvoiceModel> _employeeInvoices = [];
  bool _isLoading = false;
  DateTime? _startDate;
  DateTime? _endDate;
  String _selectedPeriod = 'last_30_days';

  final SalesRepository _salesRepository = SalesRepository();

  @override
  void initState() {
    super.initState();
    // Load employees when page initializes
    context.read<EmployeesBloc>().add(LoadAllEmployees());
  }

  Future<void> _loadEmployeeInvoices() async {
    if (_selectedEmployee == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Calculate date range based on selected period
      DateTime startDate;
      DateTime endDate = DateTime.now();

      switch (_selectedPeriod) {
        case 'last_30_days':
          startDate = DateTime.now().subtract(const Duration(days: 30));
          break;
        case 'custom':
          if (_startDate == null || _endDate == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('يرجى تحديد تاريخ البداية والنهاية')),
            );
            setState(() {
              _isLoading = false;
            });
            return;
          }
          startDate = _startDate!;
          endDate = _endDate!;
          break;
        default:
          startDate = DateTime.now().subtract(const Duration(days: 30));
      }

      // Get all invoices and filter by specialist_id and date range
      final allInvoices = await _salesRepository.getAllInvoicesForCommissions(
        page: 1,
        limit: 1000, // Get all invoices
      );

      _employeeInvoices = allInvoices.where((invoice) {
        // Filter by specialist_id
        if (invoice.specialistId != _selectedEmployee!.id) return false;
        
        // Filter by date range
        if (invoice.createdAt.isBefore(startDate) || 
            invoice.createdAt.isAfter(endDate.add(const Duration(days: 1)))) {
          return false;
        }
        
        return true;
      }).toList();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
      );
    }
  }

  double _calculateTotalCommissions() {
    return _employeeInvoices
        .where((invoice) => invoice.isCommissionSale)
        .fold(0.0, (sum, invoice) => sum + invoice.commissionAmount);
  }

  int _getTotalInvoicesCount() {
    return _employeeInvoices.length;
  }

  double _getTotalSalesAmount() {
    return _employeeInvoices.fold(0.0, (sum, invoice) => sum + invoice.finalAmount);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Header with employee selector
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'العمولات والمبيعات',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 16.h),
                
                // Employee selector
                BlocBuilder<EmployeesBloc, EmployeesState>(
                  builder: (context, state) {
                    if (state is EmployeesLoaded) {
                      final specialists = state.employees
                          .where((e) => e.isSpecialist)
                          .toList();
                      
                      return DropdownButtonFormField<EmployeeModel>(
                        value: _selectedEmployee,
                        decoration: InputDecoration(
                          labelText: 'اختر الأخصائي',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                        items: specialists.map((employee) {
                          return DropdownMenuItem(
                            value: employee,
                            child: Text(employee.name),
                          );
                        }).toList(),
                        onChanged: (employee) {
                          setState(() {
                            _selectedEmployee = employee;
                            _employeeInvoices.clear();
                          });
                          if (employee != null) {
                            _loadEmployeeInvoices();
                          }
                        },
                      );
                    }
                    return const CircularProgressIndicator();
                  },
                ),
                
                SizedBox(height: 16.h),
                
                // Period selector
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: InputDecoration(
                          labelText: 'الفترة الزمنية',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: 'last_30_days',
                            child: Text('آخر 30 يوم'),
                          ),
                          DropdownMenuItem(
                            value: 'custom',
                            child: Text('فترة مخصصة'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                          if (_selectedEmployee != null) {
                            _loadEmployeeInvoices();
                          }
                        },
                      ),
                    ),
                    
                    if (_selectedPeriod == 'custom') ...[
                      SizedBox(width: 16.w),
                      Expanded(
                        child: TextButton(
                          onPressed: () async {
                            final dateRange = await showDateRangePicker(
                              context: context,
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now(),
                              initialDateRange: _startDate != null && _endDate != null
                                  ? DateTimeRange(start: _startDate!, end: _endDate!)
                                  : null,
                            );
                            if (dateRange != null) {
                              setState(() {
                                _startDate = dateRange.start;
                                _endDate = dateRange.end;
                              });
                              if (_selectedEmployee != null) {
                                _loadEmployeeInvoices();
                              }
                            }
                          },
                          child: Text(
                            _startDate != null && _endDate != null
                                ? '${_startDate!.day}/${_startDate!.month} - ${_endDate!.day}/${_endDate!.month}'
                                : 'اختر التاريخ',
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          
          // Statistics cards
          if (_selectedEmployee != null) ...[
            Container(
              padding: EdgeInsets.all(16.w),
              child: Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'إجمالي العمولات',
                      '${_calculateTotalCommissions().toStringAsFixed(2)} د.ا',
                      Icons.monetization_on,
                      Colors.green,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: _buildStatCard(
                      'عدد الفواتير',
                      '${_getTotalInvoicesCount()}',
                      Icons.receipt_long,
                      Colors.blue,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: _buildStatCard(
                      'إجمالي المبيعات',
                      '${_getTotalSalesAmount().toStringAsFixed(2)} د.ا',
                      Icons.trending_up,
                      Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          // Invoices list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _selectedEmployee == null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.person_search,
                              size: 64.sp,
                              color: AppColors.textSecondary,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              'اختر أخصائي لعرض مبيعاته وعمولاته',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      )
                    : _employeeInvoices.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.receipt_long_outlined,
                                  size: 64.sp,
                                  color: AppColors.textSecondary,
                                ),
                                SizedBox(height: 16.h),
                                Text(
                                  'لا توجد فواتير في هذه الفترة',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: EdgeInsets.all(16.w),
                            itemCount: _employeeInvoices.length,
                            itemBuilder: (context, index) {
                              final invoice = _employeeInvoices[index];
                              return _buildInvoiceCard(invoice);
                            },
                          ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32.sp),
          SizedBox(height: 8.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceCard(SalesInvoiceModel invoice) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: invoice.isCommissionSale 
              ? Colors.green.withValues(alpha: 0.3)
              : AppColors.border,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'فاتورة #${invoice.invoiceNumber}',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      invoice.patient?.name ?? 'غير محدد',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: invoice.paymentStatus == PaymentStatus.paid
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      invoice.paymentStatus.arabicName,
                      style: TextStyle(
                        fontSize: 11.sp,
                        fontWeight: FontWeight.w600,
                        color: invoice.paymentStatus == PaymentStatus.paid
                            ? Colors.green
                            : Colors.orange,
                      ),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    '${invoice.finalAmount.toStringAsFixed(0)} د.ا',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          SizedBox(height: 12.h),
          
          // Details
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  Icons.calendar_today,
                  'التاريخ',
                  '${invoice.createdAt.day}/${invoice.createdAt.month}/${invoice.createdAt.year}',
                ),
              ),
              Expanded(
                child: _buildDetailItem(
                  Icons.shopping_cart,
                  'المنتجات',
                  '${invoice.items.length}',
                ),
              ),
              if (invoice.isCommissionSale)
                Expanded(
                  child: _buildDetailItem(
                    Icons.monetization_on,
                    'العمولة',
                    '${invoice.commissionAmount.toStringAsFixed(2)} د.ا',
                    color: Colors.green,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(IconData icon, String label, String value, {Color? color}) {
    return Column(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: color ?? AppColors.textSecondary,
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          style: TextStyle(
            fontSize: 10.sp,
            color: AppColors.textSecondary,
          ),
        ),
        SizedBox(height: 2.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w600,
            color: color ?? AppColors.textPrimary,
          ),
        ),
      ],
    );
  }
}
