-- Add specialist_id column to sales_invoices table
-- This column will track which specialist sold the products

-- Add the column if it doesn't exist
ALTER TABLE sales_invoices 
ADD COLUMN IF NOT EXISTS specialist_id UUID REFERENCES admins(id);

-- Add comment to explain the column
COMMENT ON COLUMN sales_invoices.specialist_id IS 'ID of the specialist who sold the products (references admins table)';

-- Create index for better performance on specialist queries
CREATE INDEX IF NOT EXISTS idx_sales_invoices_specialist_id ON sales_invoices(specialist_id);

-- Update existing invoices to set specialist_id to created_by if it's null
-- This is a one-time migration to populate existing data
UPDATE sales_invoices 
SET specialist_id = created_by 
WHERE specialist_id IS NULL AND created_by IS NOT NULL;

-- Optional: Create a view for specialist sales statistics
CREATE OR REPLACE VIEW specialist_sales_stats AS
SELECT 
    a.id as specialist_id,
    a.name as specialist_name,
    COUNT(si.id) as total_invoices,
    SUM(si.final_amount) as total_sales_amount,
    SUM(CASE WHEN si.is_commission_sale THEN si.commission_amount ELSE 0 END) as total_commissions,
    COUNT(CASE WHEN si.is_commission_sale THEN 1 END) as commission_invoices_count,
    AVG(si.final_amount) as average_invoice_amount,
    MIN(si.created_at) as first_sale_date,
    MAX(si.created_at) as last_sale_date
FROM admins a
LEFT JOIN sales_invoices si ON a.id = si.specialist_id
WHERE a.employee_type = 'specialist'
GROUP BY a.id, a.name
ORDER BY total_sales_amount DESC;

-- Grant permissions to view the statistics
GRANT SELECT ON specialist_sales_stats TO authenticated;

-- Add RLS policy for the view if needed
-- CREATE POLICY "Users can view specialist stats" ON specialist_sales_stats
--     FOR SELECT USING (auth.role() = 'authenticated');
