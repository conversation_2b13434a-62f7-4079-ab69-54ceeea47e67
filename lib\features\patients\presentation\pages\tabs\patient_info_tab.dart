import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:developer' as developer;
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/models/patient_model.dart';
import '../../../../../core/models/treatment_type.dart';

class PatientInfoTab extends StatefulWidget {
  final PatientModel patient;
  final Function(PatientModel)? onPatientUpdated;

  const PatientInfoTab({
    super.key,
    required this.patient,
    this.onPatientUpdated,
  });

  @override
  State<PatientInfoTab> createState() => _PatientInfoTabState();
}

class _PatientInfoTabState extends State<PatientInfoTab> {
  late PatientModel _currentPatient;

  @override
  void initState() {
    super.initState();
    _currentPatient = widget.patient;
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildSectionHeader('البيانات الشخصية', Icons.person),
            SizedBox(height: 16.h),

            // Personal Information Cards
            _buildInfoCard('الاسم الكامل', _currentPatient.name, Icons.person),
            _buildInfoCard(
              'العمر',
              '${_currentPatient.age ?? 'غير محدد'} سنة',
              Icons.cake,
            ),
            _buildInfoCard(
              'تاريخ الميلاد',
              _currentPatient.birthDate != null
                  ? DateFormat('dd/MM/yyyy').format(_currentPatient.birthDate!)
                  : 'غير محدد',
              Icons.calendar_today,
            ),
            _buildPhoneInfoCard(
              'رقم الهاتف',
              _currentPatient.phone ?? 'غير محدد',
              Icons.phone,
            ),
            _buildInfoCard(
              'البريد الإلكتروني',
              _currentPatient.email ?? 'غير محدد',
              Icons.email,
            ),
            _buildInfoCard(
              'الجنس',
              _getGenderText(_currentPatient.gender),
              Icons.wc,
            ),

            SizedBox(height: 24.h),

            // Physical Information
            _buildSectionHeader('المعلومات الجسدية', Icons.fitness_center),
            SizedBox(height: 16.h),

            // Treatment Types Card
            _buildTreatmentTypesCard(),

            SizedBox(height: 24.h),

            // Account Information
            _buildSectionHeader('معلومات الحساب', Icons.account_circle),
            SizedBox(height: 16.h),

            _buildInfoCard(
              'نوع العضوية',
              _currentPatient.isPremium ? 'عضوية مميزة' : 'عضوية عادية',
              _currentPatient.isPremium ? Icons.star : Icons.person,
            ),
            _buildInfoCard(
              'تاريخ التسجيل',
              DateFormat(
                'dd/MM/yyyy - HH:mm',
              ).format(_currentPatient.createdAt),
              Icons.date_range,
            ),
            _buildInfoCard(
              'آخر تحديث',
              DateFormat(
                'dd/MM/yyyy - HH:mm',
              ).format(_currentPatient.updatedAt),
              Icons.update,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onRefresh() async {
    // محاكاة تحديث البيانات
    await Future.delayed(const Duration(seconds: 1));
    // يمكن إضافة منطق تحديث البيانات هنا إذا لزم الأمر
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(icon, color: AppColors.primary, size: 20.w),
        ),
        SizedBox(width: 12.w),
        Text(
          title,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(String label, String value, IconData icon) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primary, size: 20.w),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  String _getGenderText(String? gender) {
    switch (gender?.toLowerCase()) {
      case 'male':
        return 'ذكر';
      case 'female':
        return 'أنثى';
      default:
        return 'غير محدد';
    }
  }

  Widget _buildTreatmentTypesCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.medical_services, color: AppColors.primary, size: 24),
              SizedBox(width: 8.w),
              Text(
                'أنواع العلاج',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          if (_currentPatient.treatmentTypes.isNotEmpty)
            Column(
              children: [
                TreatmentTypeHelper.buildDisplayChips(_currentPatient.treatmentTypes),
                SizedBox(height: 12.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton.icon(
                    onPressed: _showEditTreatmentTypesDialog,
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('تعديل أنواع العلاج'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primary,
                    ),
                  ),
                ),
              ],
            )
          else
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppColors.gray50,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: AppColors.gray200),
              ),
              child: Column(
                children: [
                  Icon(Icons.info_outline, color: AppColors.textSecondary, size: 32),
                  SizedBox(height: 8.h),
                  Text(
                    'لم يتم تحديد نوع العلاج',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  TextButton.icon(
                    onPressed: _showEditTreatmentTypesDialog,
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('إضافة أنواع العلاج'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }



  Widget _buildPhoneInfoCard(String label, String value, IconData icon) {
    final hasPhone =
        _currentPatient.phone != null && _currentPatient.phone!.isNotEmpty;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primary, size: 20.w),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    if (hasPhone) ...[
                      SizedBox(width: 8.w),
                      // Call button
                      InkWell(
                        onTap: () => _makePhoneCall(_currentPatient.phone!),
                        child: Container(
                          padding: EdgeInsets.all(6.w),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                          child: Icon(
                            Icons.phone,
                            size: 16.w,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                      SizedBox(width: 4.w),
                      // Copy button
                      InkWell(
                        onTap: () => _copyToClipboard(_currentPatient.phone!),
                        child: Container(
                          padding: EdgeInsets.all(6.w),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                          child: Icon(
                            Icons.copy,
                            size: 16.w,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _makePhoneCall(String phoneNumber) async {
    try {
      // تنظيف رقم الهاتف من المسافات والرموز الخاصة
      final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

      // استخدام tel: scheme مع DIAL mode
      final Uri phoneUri = Uri.parse('tel:$cleanNumber');

      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('❌ Error making phone call: $e');
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
  }

  void _showEditTreatmentTypesDialog() {
    List<TreatmentType> selectedTypes = List.from(_currentPatient.treatmentTypes);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('تعديل أنواع العلاج'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: TreatmentType.values.map((type) {
                final isSelected = selectedTypes.contains(type);
                return CheckboxListTile(
                  title: Text(type.arabicName),
                  value: isSelected,
                  onChanged: (bool? value) {
                    setDialogState(() {
                      if (value == true) {
                        selectedTypes.add(type);
                      } else {
                        selectedTypes.remove(type);
                      }
                    });
                  },
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (selectedTypes.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى اختيار نوع علاج واحد على الأقل'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                  return;
                }

                try {
                  // Update patient treatment types
                  await Supabase.instance.client
                      .from('patients')
                      .update({
                        'treatment_types': TreatmentType.toStringList(selectedTypes),
                        'updated_at': DateTime.now().toIso8601String(),
                      })
                      .eq('id', _currentPatient.id);

                  // Update local state
                  final updatedPatient = _currentPatient.copyWith(
                    treatmentTypes: selectedTypes,
                    updatedAt: DateTime.now(),
                  );

                  setState(() {
                    _currentPatient = updatedPatient;
                  });

                  // Notify parent
                  widget.onPatientUpdated?.call(updatedPatient);

                  if (mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم تحديث أنواع العلاج بنجاح'),
                        backgroundColor: AppColors.success,
                      ),
                    );
                  }
                } catch (e) {
                  developer.log('Error updating treatment types: $e', name: 'PatientInfoTab');
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('خطأ في تحديث أنواع العلاج'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              },
              child: const Text('حفظ'),
            ),
          ],
        ),
      ),
    );
  }
}
