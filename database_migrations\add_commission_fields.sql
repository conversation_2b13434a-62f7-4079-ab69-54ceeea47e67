-- Add commission fields to sales_invoices table
ALTER TABLE sales_invoices 
ADD COLUMN IF NOT EXISTS is_commission_sale BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS commission_percentage DECIMAL(5,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS commission_amount DECIMAL(10,2) DEFAULT 0.00;

-- Add comment to explain the fields
COMMENT ON COLUMN sales_invoices.is_commission_sale IS 'Whether this is a commission sale (5% discount)';
COMMENT ON COLUMN sales_invoices.commission_percentage IS 'Commission percentage (usually 5%)';
COMMENT ON COLUMN sales_invoices.commission_amount IS 'Commission amount in currency';

-- Update existing invoices to have default values
UPDATE sales_invoices 
SET 
  is_commission_sale = FALSE,
  commission_percentage = 0.00,
  commission_amount = 0.00
WHERE 
  is_commission_sale IS NULL 
  OR commission_percentage IS NULL 
  OR commission_amount IS NULL;
