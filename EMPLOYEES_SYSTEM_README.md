# نظام إدارة الموظفين - مركز السمع والنطق والسلوك

## نظرة عامة
تم تطوير نظام شامل لإدارة الموظفين في مركز السمع والنطق والسلوك، يدعم أنواع مختلفة من الموظفين مع صلاحيات متدرجة ونظام جداول عمل متقدم.

## أنواع الموظفين

### 1. المدير العام (Super Admin)
- **الصلاحيات**: جميع الصلاحيات
- **الوصول**: كامل لجميع أجزاء النظام
- **المسؤوليات**: إدارة النظام بالكامل، إضافة/حذف الموظفين، إدارة الصلاحيات

### 2. المدير (Admin)
- **الصلاحيات**: معظم الصلاحيات عدا إدارة المديرين الآخرين
- **الوصول**: إدارة المواعيد، المرضى، الموظفين، التقارير
- **المسؤوليات**: الإشراف على العمليات اليومية

### 3. موظف الاستقبال (Receptionist)
- **الصلاحيات**: محدودة - المواعيد والمرضى فقط
- **الوصول**: حجز المواعيد، إدارة بيانات المرضى
- **المسؤوليات**: استقبال المرضى وإدارة المواعيد

### 4. الأخصائي (Specialist)
- **الصلاحيات**: المواعيد الخاصة به، جدوله الشخصي، بيانات مرضاه
- **الوصول**: مواعيده، جدول عمله، ملفات المرضى المحولين إليه
- **المسؤوليات**: تقديم الخدمات العلاجية، إدارة جدوله

## التخصصات المتاحة

### 1. أخصائي السمع (Audiologist)
- **اللون**: أزرق (#2196F3)
- **الأيقونة**: hearing
- **الوصف**: تشخيص وعلاج مشاكل السمع

### 2. أخصائي النطق (Speech Therapist)
- **اللون**: أخضر (#4CAF50)
- **الأيقونة**: record_voice_over
- **الوصف**: تشخيص وعلاج مشاكل النطق واللغة

### 3. أخصائي السلوك (Behavior Therapist)
- **اللون**: برتقالي (#FF9800)
- **الأيقونة**: psychology
- **الوصف**: تعديل السلوك والتأهيل

### 4. أخصائي التخاطب (Communication Specialist)
- **اللون**: بنفسجي (#9C27B0)
- **الأيقونة**: chat
- **الوصف**: تطوير مهارات التواصل

### 5. أخصائي التعليم الخاص (Special Education)
- **اللون**: أحمر (#F44336)
- **الأيقونة**: school
- **الوصف**: التعليم الخاص وصعوبات التعلم

## الميزات الجديدة

### 1. إدارة الموظفين
- ✅ إضافة/تعديل/حذف الموظفين
- ✅ تصنيف الموظفين حسب النوع
- ✅ ربط الأخصائيين بالتخصصات
- ✅ تفعيل/إلغاء تفعيل الموظفين
- ✅ إدارة معلومات الموظف (الراتب، العنوان، الهاتف، إلخ)

### 2. نظام الجداول
- ✅ جداول عمل أسبوعية لكل موظف
- ✅ تحديد مدة الجلسة وفترات الراحة
- ✅ إدارة الاستثناءات (إجازات، أيام مرضية)
- ✅ حساب المواعيد المتاحة تلقائياً

### 3. نظام المواعيد المحدث
- ✅ ربط المواعيد بالموظفين بدلاً من الفترات الثابتة
- ✅ أنواع مواعيد مختلفة (استشارة، علاج، تقييم، متابعة)
- ✅ مدة مخصصة لكل موعد
- ✅ ملاحظات الجلسة والموعد التالي

### 4. نظام الصلاحيات المحدث
- ✅ صلاحيات متدرجة حسب نوع الموظف
- ✅ تحكم دقيق في الوصول للميزات
- ✅ حماية البيانات الحساسة

## قاعدة البيانات

### الجداول الجديدة
1. **specializations** - التخصصات الطبية
2. **employee_schedules** - جداول عمل الموظفين
3. **employee_schedule_exceptions** - استثناءات الجداول

### الجداول المحدثة
1. **admins** → **employees** - إضافة حقول جديدة
2. **appointments** - ربط بالموظفين وميزات جديدة

### الدوال المخصصة
1. **get_available_time_slots()** - حساب المواعيد المتاحة
2. **is_employee_available()** - فحص توفر الموظف
3. **get_employee_stats()** - إحصائيات الموظف

## واجهة المستخدم

### صفحة الموظفين الجديدة
- **الموقع**: تبويب منفصل في Bottom Navigation
- **التبويبات**:
  - الموظفين: إدارة قائمة الموظفين
  - جداول العمل: إدارة مواعيد الموظفين
  - التخصصات: إدارة التخصصات الطبية

### الميزات التفاعلية
- ✅ بحث وتصفية الموظفين
- ✅ عرض بصري للتخصصات بالألوان
- ✅ نوافذ حوارية لإضافة/تعديل البيانات
- ✅ تأكيدات الحذف والتعديل

## الأمان والحماية

### Row Level Security (RLS)
- ✅ كل موظف يرى بياناته فقط
- ✅ الأخصائيون يديرون جداولهم ومواعيدهم
- ✅ المديرون يصلون لجميع البيانات
- ✅ حماية البيانات الحساسة

### التشفير والمصادقة
- ✅ استخدام Supabase Auth
- ✅ JWT tokens آمنة
- ✅ تشفير كلمات المرور

## كيفية الاستخدام

### إضافة موظف جديد
1. اذهب إلى تبويب "الموظفين"
2. انقر "إضافة موظف"
3. املأ البيانات المطلوبة
4. اختر نوع الموظف والتخصص (للأخصائيين)
5. احفظ البيانات

### إدارة جدول الموظف
1. اذهب إلى تبويب "جداول العمل"
2. اختر الموظف
3. أضف/عدل أوقات العمل لكل يوم
4. حدد مدة الجلسة وفترات الراحة

### حجز موعد
1. اذهب إلى تبويب "الحجوزات"
2. اختر التاريخ والأخصائي
3. النظام سيعرض المواعيد المتاحة تلقائياً
4. اختر الموعد واحجز

## التطوير المستقبلي

### الميزات المخططة
- [ ] تقارير أداء الموظفين
- [ ] نظام إشعارات متقدم
- [ ] تطبيق جوال للأخصائيين
- [ ] تكامل مع أنظمة المحاسبة
- [ ] نظام تقييم الأداء

### التحسينات التقنية
- [ ] تحسين الأداء
- [ ] إضافة المزيد من الاختبارات
- [ ] تحسين واجهة المستخدم
- [ ] دعم اللغات المتعددة

## الدعم والصيانة

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**تاريخ آخر تحديث**: يوليو 2025
**الإصدار**: 2.0.0
**المطور**: Augment Agent
