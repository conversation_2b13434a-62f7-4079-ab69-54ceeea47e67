import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/holiday_model.dart';
import '../../data/repositories/appointments_repository.dart';
import '../../../../core/network/supabase_client.dart';

import '../bloc/appointments_bloc.dart';
import '../bloc/appointments_event.dart';
import '../bloc/appointments_state.dart';
import '../bloc/time_slots_bloc.dart';
import '../bloc/time_slots_event.dart';
import '../bloc/holidays_bloc.dart';
import '../bloc/holidays_event.dart';
import '../bloc/clinic_info_bloc.dart';
import '../bloc/clinic_info_event.dart';

import '../widgets/enhanced_patient_details_dialog.dart';
import '../widgets/enhanced_appointment_card.dart';
import 'professional_schedules_page.dart';
import 'holidays_page.dart';
import 'clinic_info_page.dart';
import 'create_appointment_page.dart';
import 'edit_appointment_page.dart';
import 'print_appointment_invoice_page.dart';
import 'print_multiple_appointments_invoice_page.dart';
import 'print_single_appointment_invoice_page.dart';
import '../../../patients/presentation/pages/medical_record_page.dart';

class AppointmentsPage extends StatefulWidget {
  final bool isVisible;

  const AppointmentsPage({
    super.key,
    this.isVisible = true, // Always visible by default (first page)
  });

  @override
  State<AppointmentsPage> createState() => _AppointmentsPageState();
}

class _AppointmentsPageState extends State<AppointmentsPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _tabController;
  DateTime selectedDate = DateTime.now();
  HolidayModel? selectedDateHoliday;


  // Track if data has been loaded for each tab
  bool _appointmentsLoaded = false;
  bool _timeSlotsLoaded = false;
  bool _holidaysLoaded = false;
  bool _clinicInfoLoaded = false;

  @override
  bool get wantKeepAlive => true; // Keep state alive

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);

    // Add listener to tab changes - load data only when needed
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        debugPrint('🔄 Tab changed to index: ${_tabController.index}');
        switch (_tabController.index) {
          case 0:
            if (!_appointmentsLoaded) {
              debugPrint('📋 Loading appointments for tab 0 (first time)');
              _loadAppointmentsForDate(selectedDate);
              _appointmentsLoaded = true;
            }
            break;
          case 1:
            if (!_timeSlotsLoaded) {
              debugPrint('⏰ Loading time slots for tab 1 (first time)');
              context.read<TimeSlotsBloc>().add(LoadAllTimeSlots());
              _timeSlotsLoaded = true;
            }
            break;
          case 2:
            if (!_holidaysLoaded) {
              debugPrint('🎉 Loading holidays for tab 2 (first time)');
              context.read<HolidaysBloc>().add(LoadAllHolidays());
              _holidaysLoaded = true;
            }
            break;
          case 3:
            if (!_clinicInfoLoaded) {
              debugPrint('🏥 Loading clinic info for tab 3 (first time)');
              context.read<ClinicInfoBloc>().add(const LoadAllClinicInfo());
              _clinicInfoLoaded = true;
            }
            break;
        }
      }
    });

    // Load only today's appointments when page initializes (first tab)
    _loadAppointmentsForDate(selectedDate);
    _appointmentsLoaded = true;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(AppStrings.appointments),
        backgroundColor: AppColors.white,
        elevation: 0,

        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          tabs: const [
            Tab(text: 'عرض الحجوزات'),
            Tab(text: 'حجز موعد جديد'),
            Tab(text: 'مواعيد الأخصائيين'),
            Tab(text: 'أيام الإجازات'),
            Tab(text: 'معلومات العيادة'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Appointments listener for notifications only
          BlocListener<AppointmentsBloc, AppointmentsState>(
            listener: (context, state) {
              debugPrint('🔄 BlocListener: Received state: ${state.runtimeType}');

              if (state is AppointmentsError) {
                debugPrint('❌ BlocListener: AppointmentsError received: ${state.message}');
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: AppColors.error,
                  ),
                );
              } else if (state is AppointmentCreated) {
                debugPrint('✅ BlocListener: AppointmentCreated received');
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم إضافة الموعد بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              } else if (state is AppointmentUpdated) {
                debugPrint('✅ BlocListener: AppointmentUpdated received');
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تحديث الموعد بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              } else if (state is AppointmentStatusUpdated) {
                debugPrint('✅ BlocListener: AppointmentStatusUpdated received');
                debugPrint('📋 BlocListener: Updated appointment ID: ${state.appointment.id}');
                debugPrint('📋 BlocListener: Updated appointment status: ${state.appointment.status}');
              } else if (state is AppointmentDeleted) {
                debugPrint('✅ BlocListener: AppointmentDeleted received');
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف الموعد بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              }
            },
            child: const SizedBox.shrink(),
          ),

          // TabBarView - always visible regardless of appointments state
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Tab 1: Appointments View
                _buildEnhancedAppointmentsView(),

                // Tab 2: Create New Appointment
                const CreateAppointmentPage(),

                // Tab 3: Professional Schedules Management
                BlocProvider.value(
                  value: context.read<TimeSlotsBloc>(),
                  child: const ProfessionalSchedulesPage(),
                ),

                // Tab 4: Holidays Management
                BlocProvider.value(
                  value: context.read<HolidaysBloc>(),
                  child: const HolidaysPage(),
                ),

                // Tab 5: Clinic Info
                BlocProvider.value(
                  value: context.read<ClinicInfoBloc>(),
                  child: const ClinicInfoPage(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _loadAppointmentsForDate(DateTime date) {
    context.read<AppointmentsBloc>().add(LoadAppointmentsByDate(date: date));
    _checkHolidayForDate(date);
  }

  Future<void> _checkHolidayForDate(DateTime date) async {
    try {
      final dateString = date.toIso8601String().split('T')[0];
      final response =
          await SupabaseConfig.holidays
              .select()
              .eq('date', dateString)
              .maybeSingle();

      if (mounted) {
        setState(() {
          selectedDateHoliday =
              response != null ? HolidayModel.fromJson(response) : null;
        });
      }
    } catch (e) {
      debugPrint('❌ Error checking holiday for date: $e');
      if (mounted) {
        setState(() {
          selectedDateHoliday = null;
        });
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: AppColors.primary),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
      _loadAppointmentsForDate(selectedDate);
    }
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }



  String _formatTimeTo12Hour(String? timeString) {
    if (timeString == null || timeString.isEmpty) {
      return 'غير محدد';
    }

    try {
      // Parse time string (assuming format HH:mm or HH:mm:ss)
      final parts = timeString.split(':');
      if (parts.length < 2) return timeString;

      int hour = int.parse(parts[0]);
      int minute = int.parse(parts[1]);

      String period = hour >= 12 ? 'م' : 'ص';

      // Convert to 12-hour format
      if (hour == 0) {
        hour = 12; // 12 AM
      } else if (hour > 12) {
        hour = hour - 12; // PM hours
      }

      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return timeString; // Return original if parsing fails
    }
  }

  void _updateAppointmentStatus(
    String appointmentId,
    String status, [
    String? cancellationReason,
  ]) async {
    debugPrint('🔄 Starting appointment status update...');
    debugPrint('📋 Appointment ID: $appointmentId');
    debugPrint('📋 New Status: $status');
    debugPrint('📋 Cancellation Reason: $cancellationReason');

    // إظهار مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      debugPrint('🚀 Dispatching UpdateAppointmentStatus event to bloc...');

      // تحديث حالة الحجز
      context.read<AppointmentsBloc>().add(
        UpdateAppointmentStatus(appointmentId: appointmentId, status: status),
      );

      debugPrint('✅ UpdateAppointmentStatus event dispatched successfully');

      // TODO: إضافة إرسال الإشعارات للمرضى عند توفر نظام المصادقة للمرضى
      // حالياً لا يوجد عمود auth_id في جدول patients

      // إخفاء مؤشر التحميل وإعادة تحميل البيانات
      Future.delayed(const Duration(milliseconds: 1000), () {
        debugPrint('⏰ Delayed execution started...');
        if (mounted) {
          debugPrint('🔄 Widget is still mounted, proceeding with UI updates...');
          Navigator.of(context).pop(); // إخفاء مؤشر التحميل
          debugPrint('✅ Loading dialog closed');

          // إعادة تحميل البيانات للتاريخ المحدد
          debugPrint('🔄 Reloading appointments for date: $selectedDate');
          context.read<AppointmentsBloc>().add(
            LoadAppointmentsByDate(date: selectedDate),
          );
          debugPrint('✅ LoadAppointmentsByDate event dispatched');

          // إظهار رسالة نجاح
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تحديث حالة الحجز بنجاح'),
              backgroundColor: AppColors.success,
              duration: const Duration(seconds: 2),
            ),
          );
          debugPrint('✅ Success message shown');
        } else {
          debugPrint('⚠️ Widget is no longer mounted, skipping UI updates');
        }
      });
    } catch (e) {
      debugPrint('❌ Error in _updateAppointmentStatus: $e');
      debugPrint('❌ Error type: ${e.runtimeType}');
      debugPrint('❌ Stack trace: ${StackTrace.current}');

      // إخفاء مؤشر التحميل في حالة الخطأ
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الحجز: ${e.toString()}'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
        debugPrint('❌ Error message shown to user');
      }
    }
  }



  Future<void> _showCancellationDialog(String appointmentId) async {
    String? selectedReason;
    final reasons = [
      'ظروف طارئة للطبيب',
      'إغلاق العيادة لظروف خاصة',
      'عطل فني في العيادة',
      'طلب من المريض',
      'تعارض في المواعيد',
      'أخرى',
    ];

    await showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text(
              'سبب إلغاء الموعد',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('يرجى اختيار سبب إلغاء الموعد:'),
                const SizedBox(height: 16),
                ...reasons.map(
                  (reason) => RadioListTile<String>(
                    title: Text(reason),
                    value: reason,
                    groupValue: selectedReason,
                    onChanged: (value) {
                      selectedReason = value;
                      Navigator.of(context).pop();
                      _updateAppointmentStatus(
                        appointmentId,
                        'cancelled',
                        selectedReason,
                      );
                    },
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'booked':
      case 'محجوز':
        return AppColors.primary;
      case 'مجدول':
      case 'scheduled':
        return AppColors.warning;
      case 'مؤكد':
      case 'confirmed':
        return AppColors.primary;
      case 'مكتمل':
      case 'completed':
        return AppColors.success;
      case 'ملغي':
      case 'cancelled':
        return AppColors.error;
      case 'لم يحضر':
      case 'no_show':
        return AppColors.textSecondary;
      case 'available':
      case 'متاح':
        return AppColors.gray400;
      default:
        return AppColors.textSecondary;
    }
  }

  Widget _buildEnhancedAppointmentsView() {
    return BlocBuilder<AppointmentsBloc, AppointmentsState>(
      builder: (context, state) {
        if (state is AppointmentsLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is AppointmentsLoaded) {
          return _buildAppointmentsList(state.appointments);
        }

        if (state is AppointmentsError) {
          return _buildErrorView(state.message);
        }

        return _buildInitialView();
      },
    );
  }

  Widget _buildAppointmentsList(List<AppointmentModel> appointments) {
    return RefreshIndicator(
      onRefresh: () => _onRefreshAppointments(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with stats and holiday check
            _buildEnhancedAppointmentsHeader(appointments),
            SizedBox(height: 20.h),

            // Appointments list (all appointments with patients)
            () {
              // If it's a holiday, show holiday message instead of empty appointments
              if (selectedDateHoliday != null) {
                return SizedBox(
                  height: MediaQuery.of(context).size.height * 0.3,
                  child: _buildHolidayView(),
                );
              }

              final appointmentsWithPatients =
                  appointments.where((a) => a.patientId != null).toList();
              if (appointmentsWithPatients.isEmpty) {
                return SizedBox(
                  height: MediaQuery.of(context).size.height * 0.4,
                  child: _buildEmptyAppointmentsView(),
                );
              }
              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: appointmentsWithPatients.length,
                itemBuilder: (context, index) {
                  final appointment = appointmentsWithPatients[index];
                  return FutureBuilder<PatientModel?>(
                    future: _getPatientById(appointment.patientId!),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Container(
                          height: 120.h,
                          margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                          child: Center(child: CircularProgressIndicator()),
                        );
                      }

                      final patient = snapshot.data;
                      if (patient == null) {
                        return SizedBox.shrink();
                      }

                      return EnhancedAppointmentCard(
                        key: ValueKey('${appointment.id}_${appointment.timeSlotId}_${appointment.updatedAt}'),
                        appointment: appointment,
                        patient: patient,
                        onTap: () => _showPatientDetails(patient, appointment),
                        onEdit: () => _editAppointment(appointment),
                        onPrint: () => _printAppointmentInvoiceEnhanced(appointment, patient),
                        onStatusChange: () => _showStatusChangeDialog(appointment),
                        onMedicalRecord: () => _navigateToMedicalRecord(patient),
                      );
                    },
                  );
                },
              );
            }(),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedAppointmentsHeader(List<AppointmentModel> appointments) {
    // Calculate statistics from appointments
    final confirmedCount = appointments.where((a) => a.status == 'confirmed').length;
    final completedCount = appointments.where((a) => a.status == 'completed').length;
    final cancelledCount = appointments.where((a) => a.status == 'cancelled').length;
    final noShowCount = appointments.where((a) => a.status == 'no_show').length;


    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date header with selector - تصغير الكارت
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isToday(selectedDate) ? 'مواعيد اليوم' : 'المواعيد',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      '${_getDayName(selectedDate.weekday)} - ${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              InkWell(
                onTap: _selectDate,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 10.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6.r),
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 14.w,
                        color: AppColors.primary,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        'تغيير',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Holiday check
          if (selectedDateHoliday != null) ...[
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.w),
              margin: EdgeInsets.only(bottom: 16.h),
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.event_busy,
                    color: AppColors.error,
                    size: 20.sp,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'يوم إجازة',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.error,
                          ),
                        ),
                        if (selectedDateHoliday!.notes?.isNotEmpty ?? false) ...[
                          SizedBox(height: 4.h),
                          Text(
                            'السبب: ${selectedDateHoliday!.notes}',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppColors.error,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],

          // إحصائيات الحجوزات في صف واحد
          Row(
            children: [
              Expanded(
                child: _buildCompactStatCard(
                  'مؤكدة',
                  confirmedCount.toString(),
                  AppColors.primary,
                  Icons.check_circle_outline,
                ),
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: _buildCompactStatCard(
                  'مكتملة',
                  completedCount.toString(),
                  AppColors.success,
                  Icons.task_alt,
                ),
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: _buildCompactStatCard(
                  'ملغية',
                  cancelledCount.toString(),
                  AppColors.error,
                  Icons.cancel_outlined,
                ),
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: _buildCompactStatCard(
                  'لم يحضر',
                  noShowCount.toString(),
                  AppColors.warning,
                  Icons.person_off_outlined,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompactStatCard(
    String title,
    String count,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16.w),
          SizedBox(height: 4.h),
          Text(
            count,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 9.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'الإثنين';
      case 2:
        return 'الثلاثاء';
      case 3:
        return 'الأربعاء';
      case 4:
        return 'الخميس';
      case 5:
        return 'الجمعة';
      case 6:
        return 'السبت';
      case 7:
        return 'الأحد';
      default:
        return 'غير محدد';
    }
  }


  Widget _buildEmptyAppointmentsView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.event_available, size: 64.w, color: AppColors.gray400),
          SizedBox(height: 16.h),
          Text(
            'لا توجد حجوزات',
            style: TextStyle(fontSize: 18.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 8.h),
          Text(
            'لا توجد مواعيد محجوزة في هذا التاريخ',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.w, color: AppColors.error),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل المواعيد',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(fontSize: 14.sp, color: AppColors.error),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed:
                () => context.read<AppointmentsBloc>().add(
                  LoadTodayAppointments(),
                ),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.event, size: 64.w, color: AppColors.gray400),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل المواعيد...',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed:
                () => context.read<AppointmentsBloc>().add(
                  LoadTodayAppointments(),
                ),
            child: const Text('تحميل المواعيد'),
          ),
        ],
      ),
    );
  }

  void _showStatusChangeDialog(AppointmentModel appointment) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تغيير حالة الحجز'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'اختر الحالة الجديدة للحجز:',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 16.h),
                // خيارات الحالة
                _buildStatusOption(
                  appointment,
                  'completed',
                  'مكتمل',
                  Icons.check_circle,
                  AppColors.success,
                ),
                SizedBox(height: 8.h),
                _buildStatusOption(
                  appointment,
                  'cancelled',
                  'ملغي',
                  Icons.cancel,
                  AppColors.error,
                ),
                SizedBox(height: 8.h),
                _buildStatusOption(
                  appointment,
                  'no_show',
                  'لم يحضر',
                  Icons.person_off,
                  AppColors.textSecondary,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  Widget _buildStatusOption(
    AppointmentModel appointment,
    String status,
    String label,
    IconData icon,
    Color color,
  ) {
    final isCurrentStatus = appointment.status == status;

    return InkWell(
      onTap:
          isCurrentStatus
              ? null
              : () {
                Navigator.of(context).pop();
                _updateAppointmentStatusWithConfirmation(
                  appointment,
                  status,
                  label,
                );
              },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color:
              isCurrentStatus ? color.withValues(alpha: 0.1) : AppColors.gray50,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isCurrentStatus ? color : AppColors.gray200,
            width: isCurrentStatus ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isCurrentStatus ? color : AppColors.textSecondary,
              size: 20.w,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight:
                      isCurrentStatus ? FontWeight.bold : FontWeight.normal,
                  color: isCurrentStatus ? color : AppColors.textPrimary,
                ),
              ),
            ),
            if (isCurrentStatus) Icon(Icons.check, color: color, size: 20.w),
          ],
        ),
      ),
    );
  }

  void _updateAppointmentStatusWithConfirmation(
    AppointmentModel appointment,
    String newStatus,
    String statusLabel,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('تأكيد تغيير الحالة'),
            content: Text(
              'هل أنت متأكد من تغيير حالة الحجز إلى "$statusLabel"؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  if (newStatus == 'cancelled') {
                    _showCancellationDialog(appointment.id);
                  } else {
                    _updateAppointmentStatus(appointment.id, newStatus);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: _getStatusColor(newStatus),
                ),
                child: const Text('تأكيد'),
              ),
            ],
          ),
    );
  }

  Future<PatientModel?> _getPatientById(String patientId) async {
    try {
      final response = await SupabaseConfig.patients
          .select('*')
          .eq('id', patientId)
          .maybeSingle();

      if (response != null) {
        return PatientModel.fromJson(response);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting patient by ID: $e');
      return null;
    }
  }

  void _navigateToPatientPage(Map<String, dynamic> patientData) async {
    final patientId = patientData['patientId'] ?? '';
    final patientName = patientData['patientName'] ?? 'غير محدد';
    final patientPhone = patientData['patientPhone'];

    if (patientId.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => EnhancedPatientDetailsDialog(
        patientId: patientId,
        patientName: patientName,
        patientPhone: patientPhone,
      ),
    );
  }

  void _showPatientDetails(PatientModel patient, AppointmentModel appointment) {
    showDialog(
      context: context,
      builder: (context) => EnhancedPatientDetailsDialog(
        patientId: patient.id,
        patientName: patient.name,
        patientPhone: patient.phone,
      ),
    );
  }

  void _navigateToMedicalRecord(PatientModel patient) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MedicalRecordPage(patient: patient),
      ),
    );
  }



  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'مؤكد';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'no_show':
        return 'لم يحضر';
      default:
        return 'غير محدد';
    }
  }

  Widget _buildHolidayCheck() {
    if (selectedDateHoliday == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(Icons.event_busy, color: AppColors.warning, size: 24.w),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'هذا اليوم عطلة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.warning,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  selectedDateHoliday!.occasionName,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (selectedDateHoliday!.notes?.isNotEmpty == true) ...[
                  SizedBox(height: 4.h),
                  Text(
                    selectedDateHoliday!.notes!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: AppColors.warning,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              selectedDateHoliday!.holidayTypeDisplayName,
              style: TextStyle(
                fontSize: 10.sp,
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _editAppointment(AppointmentModel appointment) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EditAppointmentPage(appointment: appointment),
      ),
    ).then((updatedAppointment) {
      // Refresh appointments after editing
      if (mounted) {
        debugPrint('🔄 Received updated appointment from edit page: ${updatedAppointment?.id}');
        context.read<AppointmentsBloc>().add(LoadAppointmentsByDate(date: selectedDate));
      }
    });
  }

  void _printAppointmentInvoiceEnhanced(AppointmentModel appointment, PatientModel patient) async {
    try {
      List<AppointmentModel>? groupAppointments;

      // If it's a multiple booking, get all appointments in the group
      if (appointment.isMultipleBooking && appointment.multipleBookingGroupId != null) {
        final appointmentsRepo = AppointmentsRepository();
        groupAppointments = await appointmentsRepo.getAppointmentsByGroupId(
          appointment.multipleBookingGroupId.toString(),
        );
      }

      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PrintSingleAppointmentInvoicePage(
              appointment: appointment,
              patient: patient,
              groupAppointments: groupAppointments,
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Error printing invoice: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في طباعة الفاتورة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _printAppointmentInvoice(AppointmentModel appointment, Map<String, dynamic> patientData) async {
    PatientModel? patient;

    if (appointment.patientId != null) {
      try {
        // Create patient model from data
        patient = PatientModel(
          id: patientData['patientId'] ?? '',
          name: patientData['patientName'] ?? 'غير محدد',
          phone: patientData['patientPhone'],
          gender: 'male', // Default value
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      } catch (e) {
        debugPrint('Error creating patient model: $e');
      }
    }

    if (patient != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PrintAppointmentInvoicePage(
            appointment: appointment,
            patient: patient!,
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: لا توجد بيانات المريض للطباعة'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Widget _buildHolidayView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(50.r),
            ),
            child: Icon(
              Icons.beach_access,
              size: 48.w,
              color: AppColors.warning,
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد مواعيد - يوم عطلة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.warning,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'العيادة مغلقة في هذا اليوم',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Future<void> _onRefreshAppointments() async {
    // تحديث بيانات الحجوزات للتاريخ المحدد
    context.read<AppointmentsBloc>().add(
      LoadAppointmentsByDate(date: selectedDate),
    );
    // تحديث بيانات الإجازة للتاريخ المحدد
    _checkHolidayForDate(selectedDate);
    // انتظار قصير لإظهار مؤشر التحديث
    await Future.delayed(const Duration(milliseconds: 500));
  }
}

// Widget منفصل لزر صفحة المريض مع loading state
class _PatientPageButton extends StatefulWidget {
  final VoidCallback onPressed;

  const _PatientPageButton({required this.onPressed});

  @override
  State<_PatientPageButton> createState() => _PatientPageButtonState();
}

class _PatientPageButtonState extends State<_PatientPageButton> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      onPressed:
          _isLoading
              ? null
              : () async {
                setState(() => _isLoading = true);

                // تأخير قصير لإظهار loading state
                await Future.delayed(const Duration(milliseconds: 100));

                widget.onPressed();

                // إعادة تعيين الحالة بعد فترة قصيرة
                if (mounted) {
                  await Future.delayed(const Duration(milliseconds: 500));
                  setState(() => _isLoading = false);
                }
              },
      icon:
          _isLoading
              ? SizedBox(
                width: 16.w,
                height: 16.w,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              )
              : Icon(Icons.person, size: 16.w),
      label: Text(
        _isLoading ? 'جاري التحميل...' : 'صفحة المريض',
        style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w500),
      ),
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 8.w),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
        side: BorderSide(
          color: _isLoading ? AppColors.gray300 : AppColors.primary,
          width: 1,
        ),
        foregroundColor: _isLoading ? AppColors.gray500 : AppColors.primary,
      ),
    );
  }


}
