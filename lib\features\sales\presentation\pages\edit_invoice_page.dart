import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/product_model.dart';
import '../../../../core/models/sales_invoice_model.dart';
import '../../../../core/models/invoice_item_model.dart';
import '../bloc/sales_bloc.dart';
import '../bloc/sales_event.dart';
import '../bloc/sales_state.dart';
import '../widgets/patient_selector_widget.dart';
import '../widgets/product_selector_widget.dart';
import '../widgets/invoice_items_widget.dart';
import '../widgets/invoice_summary_widget.dart';
import 'print_invoice_page.dart';

class EditInvoicePage extends StatefulWidget {
  final SalesInvoiceModel invoice;

  const EditInvoicePage({
    super.key,
    required this.invoice,
  });

  @override
  State<EditInvoicePage> createState() => _EditInvoicePageState();
}

class _EditInvoicePageState extends State<EditInvoicePage> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final _discountController = TextEditingController();
  final _dueDateController = TextEditingController();
  final _paidAmountController = TextEditingController();
  final _remainingAmountController = TextEditingController();
  final _returnAmountController = TextEditingController();
  final _returnReasonController = TextEditingController();

  // Invoice data
  late PatientModel? _selectedPatient;
  late PaymentType _paymentType;
  late List<InvoiceItemModel> _invoiceItems;
  late double _discountPercentage;
  late DateTime? _dueDate;
  late bool _isCommissionSale;
  late double _paidAmount;
  late double _remainingAmount;
  late double _returnAmount;
  late String? _returnReason;
  late InvoiceStatus _invoiceStatus;

  @override
  void initState() {
    super.initState();
    _initializeFromInvoice();
  }

  void _initializeFromInvoice() {
    _selectedPatient = widget.invoice.patient;
    _paymentType = widget.invoice.paymentType;
    _invoiceItems = List.from(widget.invoice.items);
    _discountPercentage = widget.invoice.discountPercentage;
    _dueDate = widget.invoice.dueDate;
    _isCommissionSale = widget.invoice.isCommissionSale;
    _paidAmount = widget.invoice.paidAmount;
    _remainingAmount = widget.invoice.remainingAmount;
    _returnAmount = widget.invoice.returnAmount;
    _returnReason = widget.invoice.returnReason;
    _invoiceStatus = widget.invoice.status;

    _notesController.text = widget.invoice.notes ?? '';
    _discountController.text = _discountPercentage.toString();
    _dueDateController.text = _dueDate?.toString().split(' ')[0] ?? '';
    _paidAmountController.text = _paidAmount.toString();
    _remainingAmountController.text = _remainingAmount.toString();
    _returnAmountController.text = _returnAmount.toString();
    _returnReasonController.text = _returnReason ?? '';
  }

  @override
  void dispose() {
    _notesController.dispose();
    _discountController.dispose();
    _dueDateController.dispose();
    _paidAmountController.dispose();
    _remainingAmountController.dispose();
    _returnAmountController.dispose();
    _returnReasonController.dispose();
    super.dispose();
  }

  double _calculateSubtotal() {
    return _invoiceItems.fold<double>(
      0.0,
      (sum, item) => sum + item.finalPrice,
    );
  }

  // حساب العمولة من الربح بعد تأثير خصم الفاتورة
  double _calculateCommissionFromProfit() {
    if (!_isCommissionSale) return 0.0;

    // حساب الربح بعد تأثير خصم الفاتورة على سعر المنتج النهائي
    double totalProfitAfterDiscount = 0.0;

    for (final item in _invoiceItems) {
      if (item.product != null) {
        // السعر بعد خصم المنتج
        final priceAfterProductDiscount = item.product!.discountedPrice;

        // تطبيق خصم الفاتورة على السعر
        final finalPrice = priceAfterProductDiscount * (1 - _discountPercentage / 100);

        // الربح = السعر النهائي - السعر الأصلي
        final originalPrice = item.product!.originalPrice ?? 0.0;
        final profitPerUnit = finalPrice - originalPrice;

        totalProfitAfterDiscount += (profitPerUnit * item.quantity);
      }
    }

    return totalProfitAfterDiscount * 0.05; // 5% من الربح النهائي
  }

  double _calculateFinalAmount() {
    final subtotal = _calculateSubtotal();
    final discountAmount = subtotal * (_discountPercentage / 100);
    final afterDiscount = subtotal - discountAmount;

    // For returned invoices, final amount = due amount (after discount - return amount)
    // For normal invoices, final amount = after discount
    if (_invoiceStatus == InvoiceStatus.returned) {
      return afterDiscount - _returnAmount;
    } else {
      return afterDiscount;
    }
  }

  void _updateRemainingAmount() {
    final subtotal = _calculateSubtotal();
    final discountAmount = subtotal * (_discountPercentage / 100);
    final finalAmount = subtotal - discountAmount;

    // For installment: remaining = final amount (after discount) - paid amount
    // For cash: remaining = 0
    if (_paymentType == PaymentType.installment) {
      final remaining = finalAmount - _paidAmount;
      _remainingAmount = remaining > 0 ? remaining : 0.0;
      _remainingAmountController.text = _remainingAmount.toStringAsFixed(2);
    } else {
      _remainingAmount = 0.0;
      _remainingAmountController.text = '0.00';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تعديل فاتورة ${widget.invoice.invoiceNumber}',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _updateInvoice,
            child: Text(
              'حفظ',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: BlocListener<SalesBloc, SalesState>(
        listener: (context, state) {
          if (state is InvoiceUpdated) {
            _showSuccessDialog(state.invoice);
          } else if (state is SalesError) {
            _showErrorDialog(state.message);
          }
        },
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Patient Selection
                _buildPatientSection(),
                
                SizedBox(height: 16.h),
                
                // Product Selection
                _buildProductSection(),
                
                SizedBox(height: 16.h),
                
                // Invoice Items
                if (_invoiceItems.isNotEmpty)
                  InvoiceItemsWidget(
                    items: _invoiceItems,
                    onItemUpdated: (item) {
                      // Find the item index and update it
                      final index = _invoiceItems.indexWhere((i) => i.id == item.id);
                      if (index != -1) {
                        setState(() {
                          _invoiceItems[index] = item;
                          _updateRemainingAmount();
                        });
                      }
                    },
                    onItemRemoved: (itemId) {
                      // Find the item index and remove it
                      final index = _invoiceItems.indexWhere((i) => i.id == itemId);
                      if (index != -1) {
                        setState(() {
                          _invoiceItems.removeAt(index);
                          _updateRemainingAmount();
                        });
                      }
                    },
                  ),
                
                SizedBox(height: 16.h),
                
                // Payment Type & Commission
                _buildPaymentSection(),
                
                SizedBox(height: 16.h),
                
                // Due Date (for installment)
                if (_paymentType == PaymentType.installment)
                  _buildDueDateSection(),
                
                // Payment amounts (for installment)
                if (_paymentType == PaymentType.installment)
                  _buildPaymentAmountsSection(),
                
                SizedBox(height: 16.h),
                
                // Discount
                _buildDiscountSection(),
                
                SizedBox(height: 16.h),

                // Return Section
                _buildReturnSection(),

                SizedBox(height: 16.h),

                // Notes
                _buildNotesSection(),

                SizedBox(height: 16.h),
                
                // Summary
                InvoiceSummaryWidget(
                  items: _invoiceItems,
                  discountPercentage: _discountPercentage,
                  isCommissionSale: _isCommissionSale,
                  paymentType: _paymentType,
                  paidAmount: _paidAmount,
                  remainingAmount: _remainingAmount,
                  invoiceStatus: _invoiceStatus,
                  returnAmount: _returnAmount,
                ),
                
                SizedBox(height: 20.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPatientSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'بيانات المريض',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            PatientSelectorWidget(
              selectedPatient: _selectedPatient,
              onPatientSelected: (patient) {
                setState(() {
                  _selectedPatient = patient;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.inventory,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'إضافة منتج',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            ProductSelectorWidget(
              onProductSelected: _addProductToInvoice,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.payment,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'نوع الدفع',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<PaymentType>(
                    title: Text(
                      PaymentType.cash.arabicName,
                      style: TextStyle(fontSize: 14.sp),
                    ),
                    value: PaymentType.cash,
                    groupValue: _paymentType,
                    onChanged: (value) {
                      setState(() {
                        _paymentType = value!;
                        if (value == PaymentType.cash) {
                          _dueDate = null;
                          _dueDateController.clear();
                          _paidAmount = 0.0;
                          _remainingAmount = 0.0;
                          _paidAmountController.clear();
                          _remainingAmountController.clear();
                        }
                        _updateRemainingAmount();
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<PaymentType>(
                    title: Text(
                      PaymentType.installment.arabicName,
                      style: TextStyle(fontSize: 14.sp),
                    ),
                    value: PaymentType.installment,
                    groupValue: _paymentType,
                    onChanged: (value) {
                      setState(() {
                        _paymentType = value!;
                        _updateRemainingAmount();
                      });
                    },
                  ),
                ),
              ],
            ),
            
            // Commission Sale Option (Read-only in edit mode)
            SizedBox(height: 16.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: _invoiceStatus == InvoiceStatus.returned
                    ? Colors.red.withValues(alpha: 0.1)
                    : (_isCommissionSale ? Colors.blue.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1)),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: _invoiceStatus == InvoiceStatus.returned
                      ? Colors.red.withValues(alpha: 0.3)
                      : (_isCommissionSale ? Colors.blue.withValues(alpha: 0.3) : Colors.grey.withValues(alpha: 0.3)),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _invoiceStatus == InvoiceStatus.returned
                        ? Icons.cancel
                        : (_isCommissionSale ? Icons.check_circle : Icons.info),
                    color: _invoiceStatus == InvoiceStatus.returned
                        ? Colors.red
                        : (_isCommissionSale ? Colors.blue : Colors.grey),
                    size: 20.sp,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _invoiceStatus == InvoiceStatus.returned
                              ? 'العمولة ملغية (فاتورة مرتجعة)'
                              : (_isCommissionSale ? 'بيع بعمولة (5%)' : 'بيع عادي'),
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: _invoiceStatus == InvoiceStatus.returned
                                ? Colors.red
                                : (_isCommissionSale ? Colors.blue : Colors.grey[700]),
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          _invoiceStatus == InvoiceStatus.returned
                              ? 'لا يمكن تطبيق عمولة على الفواتير المرتجعة'
                              : 'لا يمكن تعديل نوع البيع بعد إنشاء الفاتورة',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDueDateSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'تاريخ الاستحقاق',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            TextFormField(
              controller: _dueDateController,
              readOnly: true,
              decoration: InputDecoration(
                labelText: 'تاريخ الاستحقاق',
                hintText: 'اختر تاريخ الاستحقاق',
                prefixIcon: const Icon(Icons.calendar_today),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _dueDate ?? DateTime.now().add(const Duration(days: 30)),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (date != null) {
                  setState(() {
                    _dueDate = date;
                    _dueDateController.text = date.toString().split(' ')[0];
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentAmountsSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.payments,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'تفاصيل الدفع',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _paidAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'المبلغ المدفوع',
                      hintText: '0.00',
                      prefixIcon: const Icon(Icons.attach_money),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    onChanged: (value) {
                      final paidAmount = double.tryParse(value) ?? 0.0;
                      setState(() {
                        _paidAmount = paidAmount;
                        _updateRemainingAmount();
                      });
                    },
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: TextFormField(
                    controller: _remainingAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'المبلغ المتبقي',
                      hintText: '0.00',
                      prefixIcon: const Icon(Icons.money_off),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    onChanged: (value) {
                      final remainingAmount = double.tryParse(value) ?? 0.0;
                      setState(() {
                        _remainingAmount = remainingAmount;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscountSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.discount,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'الخصم',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            TextFormField(
              controller: _discountController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'نسبة الخصم (%)',
                hintText: '0',
                prefixIcon: const Icon(Icons.percent),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _discountPercentage = double.tryParse(value) ?? 0.0;
                  _updateRemainingAmount();
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReturnSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.assignment_return,
                  color: Colors.orange,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'إدارة الاسترجاع',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // Return Status
            Row(
              children: [
                Expanded(
                  child: RadioListTile<InvoiceStatus>(
                    title: Text(
                      'فاتورة عادية',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                    value: InvoiceStatus.active,
                    groupValue: _invoiceStatus,
                    onChanged: (value) {
                      setState(() {
                        _invoiceStatus = value!;
                        if (value != InvoiceStatus.returned) {
                          _returnAmount = 0.0;
                          _returnReason = null;
                          _returnAmountController.clear();
                          _returnReasonController.clear();
                        }
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<InvoiceStatus>(
                    title: Text(
                      'فاتورة مرتجعة',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                    value: InvoiceStatus.returned,
                    groupValue: _invoiceStatus,
                    onChanged: (value) {
                      setState(() {
                        _invoiceStatus = value!;
                        // Cancel commission for returned invoices
                        if (value == InvoiceStatus.returned) {
                          // Commission is automatically cancelled in calculations
                        }
                      });
                    },
                  ),
                ),
              ],
            ),

            // Return Details (if returned)
            if (_invoiceStatus == InvoiceStatus.returned) ...[
              SizedBox(height: 16.h),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _returnAmountController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'المبلغ المرتجع',
                        hintText: '0.00',
                        prefixIcon: const Icon(Icons.money_off),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _returnAmount = double.tryParse(value) ?? 0.0;
                        });
                      },
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              TextFormField(
                controller: _returnReasonController,
                maxLines: 2,
                decoration: InputDecoration(
                  labelText: 'سبب الاسترجاع',
                  hintText: 'أدخل سبب الاسترجاع...',
                  prefixIcon: const Icon(Icons.note),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                onChanged: (value) {
                  setState(() {
                    _returnReason = value.trim().isEmpty ? null : value.trim();
                  });
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'ملاحظات',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'ملاحظات إضافية',
                hintText: 'أدخل أي ملاحظات...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addProductToInvoice(ProductModel product, int quantity) {
    setState(() {
      // Check if product already exists
      final existingIndex = _invoiceItems.indexWhere((item) => item.productId == product.id);

      if (existingIndex != -1) {
        // Update existing item
        final existingItem = _invoiceItems[existingIndex];
        final newQuantity = existingItem.quantity + quantity;
        final unitPrice = product.discountedPrice; // ✅ العميل يدفع السعر بعد خصم المنتج
        final totalPrice = newQuantity * unitPrice;

        _invoiceItems[existingIndex] = existingItem.copyWith(
          quantity: newQuantity,
          unitPrice: unitPrice,
          totalPrice: totalPrice,
          finalPrice: totalPrice,
        );
      } else {
        // Add new item
        final unitPrice = product.discountedPrice; // ✅ العميل يدفع السعر بعد خصم المنتج
        final totalPrice = quantity * unitPrice;

        final newItem = InvoiceItemModel(
          id: const Uuid().v4(),
          invoiceId: widget.invoice.id,
          productId: product.id,
          product: product,
          productName: product.name,
          productCode: product.productCode,
          quantity: quantity,
          unitPrice: unitPrice,
          totalPrice: totalPrice,
          finalPrice: totalPrice,
          createdAt: DateTime.now(),
        );

        _invoiceItems.add(newItem);
      }

      _updateRemainingAmount();
    });
  }



  void _updateInvoice() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedPatient == null) {
      _showErrorDialog('يرجى اختيار المريض');
      return;
    }

    if (_invoiceItems.isEmpty) {
      _showErrorDialog('يرجى إضافة منتج واحد على الأقل');
      return;
    }

    final subtotal = _calculateSubtotal();
    final discountAmount = subtotal * (_discountPercentage / 100);
    final finalAmount = _calculateFinalAmount(); // Use the new method that handles returns

    // Calculate commission from actual profit (not for returned invoices)
    final commissionAmount = (_invoiceStatus != InvoiceStatus.returned)
        ? _calculateCommissionFromProfit()
        : 0.0;

    // Calculate payment amounts
    final paidAmount = _paymentType == PaymentType.installment ? _paidAmount : finalAmount;
    final remainingAmount = _paymentType == PaymentType.installment ? _remainingAmount : 0.0;

    // Update invoice
    final updatedInvoice = widget.invoice.copyWith(
      patientId: _selectedPatient!.id,
      patient: _selectedPatient,
      totalAmount: subtotal,
      discountPercentage: _discountPercentage,
      discountAmount: discountAmount,
      finalAmount: finalAmount,
      isCommissionSale: _isCommissionSale,
      commissionPercentage: _isCommissionSale ? 5.0 : 0.0,
      commissionAmount: commissionAmount,
      paymentType: _paymentType,
      paymentStatus: PaymentStatusExtension.calculateStatus(
        paymentType: _paymentType,
        invoiceStatus: _invoiceStatus,
        remainingAmount: remainingAmount,
      ),
      paidAmount: paidAmount,
      remainingAmount: remainingAmount,
      dueDate: _dueDate,
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      status: _invoiceStatus,
      returnAmount: _returnAmount,
      returnReason: _returnReason,
      returnedAt: _invoiceStatus == InvoiceStatus.returned ? DateTime.now() : null,
      returnedBy: _invoiceStatus == InvoiceStatus.returned ? const Uuid().v4() : null, // TODO: Get from current user
      updatedAt: DateTime.now(),
      items: _invoiceItems,
    );

    context.read<SalesBloc>().add(UpdateInvoice(
      invoice: updatedInvoice,
      items: _invoiceItems,
    ));
  }

  void _showSuccessDialog(SalesInvoiceModel updatedInvoice) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 24.sp),
            SizedBox(width: 8.w),
            Text('تم تحديث الفاتورة بنجاح'),
          ],
        ),
        content: Text('فاتورة رقم: ${updatedInvoice.invoiceNumber}'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Close edit page
            },
            child: const Text('موافق'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => PrintInvoicePage(invoice: updatedInvoice),
                ),
              );
            },
            icon: Icon(Icons.print, size: 16.sp),
            label: const Text('طباعة الفاتورة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
