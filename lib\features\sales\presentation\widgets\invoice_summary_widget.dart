import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/invoice_item_model.dart';
import '../../../../core/models/sales_invoice_model.dart';

class InvoiceSummaryWidget extends StatelessWidget {
  final List<InvoiceItemModel> items;
  final double discountPercentage;
  final bool isCommissionSale;
  final PaymentType? paymentType;
  final double paidAmount;
  final double remainingAmount;
  final InvoiceStatus? invoiceStatus;
  final double returnAmount;

  const InvoiceSummaryWidget({
    super.key,
    required this.items,
    this.discountPercentage = 0.0,
    this.isCommissionSale = false,
    this.paymentType,
    this.paidAmount = 0.0,
    this.remainingAmount = 0.0,
    this.invoiceStatus,
    this.returnAmount = 0.0,
  });

  // حساب العمولة من الربح بعد تأثير خصم الفاتورة
  double _calculateCommissionFromProfit() {
    if (!isCommissionSale) return 0.0;

    // حساب الربح بعد تأثير خصم الفاتورة على سعر المنتج النهائي
    double totalProfitAfterDiscount = 0.0;

    for (final item in items) {
      if (item.product != null) {
        // السعر بعد خصم المنتج
        final priceAfterProductDiscount = item.product!.discountedPrice;

        // تطبيق خصم الفاتورة على السعر
        final finalPrice = priceAfterProductDiscount * (1 - discountPercentage / 100);

        // الربح = السعر النهائي - السعر الأصلي
        final originalPrice = item.product!.originalPrice ?? 0.0;
        final profitPerUnit = finalPrice - originalPrice;

        totalProfitAfterDiscount += (profitPerUnit * item.quantity);
      }
    }

    return totalProfitAfterDiscount * 0.05; // 5% من الربح النهائي
  }

  @override
  Widget build(BuildContext context) {
    final subtotal = items.fold<double>(
      0.0,
      (sum, item) => sum + item.finalPrice,
    );

    final discountAmount = subtotal * (discountPercentage / 100);
    final afterDiscount = subtotal - discountAmount;

    // حساب العمولة من الربح الفعلي
    final commissionAmount = (isCommissionSale && invoiceStatus != InvoiceStatus.returned)
        ? _calculateCommissionFromProfit()
        : 0.0;

    // For returned invoices, calculate due amount correctly
    // Due amount = paid amount - return amount (what customer should get back or keep)
    final dueAmount = invoiceStatus == InvoiceStatus.returned
        ? (paidAmount - returnAmount) > 0 ? (paidAmount - returnAmount) : 0.0
        : afterDiscount;

    // For display purposes, show the due amount for returned invoices
    final total = invoiceStatus == InvoiceStatus.returned ? dueAmount : afterDiscount;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calculate,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'ملخص الفاتورة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            // Items count
            _buildSummaryRow(
              'عدد المنتجات:',
              '${items.length} منتج',
              isHighlight: false,
            ),
            
            SizedBox(height: 8.h),
            
            // Total quantity
            _buildSummaryRow(
              'إجمالي الكمية:',
              '${items.fold<int>(0, (sum, item) => sum + item.quantity)} قطعة',
              isHighlight: false,
            ),
            
            SizedBox(height: 12.h),
            Divider(color: AppColors.border),
            SizedBox(height: 12.h),
            
            // Subtotal
            _buildSummaryRow(
              'المجموع الفرعي:',
              '${subtotal.toStringAsFixed(2)} د.ا',
              isHighlight: false,
            ),
            
            SizedBox(height: 8.h),
            
            // Discount
            if (discountPercentage > 0) ...[
              _buildSummaryRow(
                'الخصم ($discountPercentage%):',
                '- ${discountAmount.toStringAsFixed(2)} د.ا',
                isHighlight: false,
                color: AppColors.error,
              ),
              SizedBox(height: 8.h),
            ],

            // Commission (informational only)
            if (isCommissionSale) ...[
              _buildSummaryRow(
                'العمولة المستحقة (5% من الربح):',
                '${commissionAmount.toStringAsFixed(2)} د.ا',
                isHighlight: false,
                color: Colors.blue,
              ),
              SizedBox(height: 8.h),
            ],

            if (discountPercentage > 0 || isCommissionSale) ...[
              Divider(color: AppColors.border),
              SizedBox(height: 12.h),
            ],
            
            // Total
            _buildSummaryRow(
              'المجموع النهائي:',
              '${total.toStringAsFixed(2)} د.ا',
              isHighlight: true,
            ),

            // Payment details based on invoice status
            if (invoiceStatus == InvoiceStatus.returned) ...[
              // For returned invoices
              SizedBox(height: 12.h),
              Divider(color: AppColors.border),
              SizedBox(height: 12.h),
              _buildSummaryRow(
                'المبلغ المرتجع:',
                '${returnAmount.toStringAsFixed(2)} د.ا',
                isHighlight: false,
                color: Colors.red,
              ),
              SizedBox(height: 8.h),
              _buildSummaryRow(
                'المبلغ المستحق:',
                '${dueAmount.toStringAsFixed(2)} د.ا',
                isHighlight: false,
                color: Colors.blue,
              ),
            ] else if (paymentType == PaymentType.installment) ...[
              // For installment invoices (not returned)
              SizedBox(height: 12.h),
              Divider(color: AppColors.border),
              SizedBox(height: 12.h),
              _buildSummaryRow(
                'المبلغ المدفوع:',
                '${paidAmount.toStringAsFixed(2)} د.ا',
                isHighlight: false,
                color: Colors.green,
              ),
              SizedBox(height: 8.h),
              _buildSummaryRow(
                'المبلغ المتبقي:',
                '${remainingAmount.toStringAsFixed(2)} د.ا',
                isHighlight: false,
                color: Colors.orange,
              ),
            ],

            if (items.isNotEmpty) ...[
              SizedBox(height: 16.h),
              _buildItemsBreakdown(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    String value, {
    bool isHighlight = false,
    Color? color,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isHighlight ? 16.sp : 14.sp,
            fontWeight: isHighlight ? FontWeight.bold : FontWeight.w500,
            color: color ?? AppColors.textPrimary,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isHighlight ? 16.sp : 14.sp,
            fontWeight: isHighlight ? FontWeight.bold : FontWeight.w600,
            color: color ?? (isHighlight ? AppColors.primary : AppColors.textPrimary),
          ),
        ),
      ],
    );
  }

  Widget _buildItemsBreakdown() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل المنتجات:',
            style: TextStyle(
              fontSize: 13.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8.h),
          ...items.map((item) => Padding(
                padding: EdgeInsets.only(bottom: 4.h),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        item.productName,
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: AppColors.textSecondary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      '${item.quantity}x',
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      '${item.unitPrice.toStringAsFixed(0)}',
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      '${item.finalPrice.toStringAsFixed(0)} د.ا',
                      style: TextStyle(
                        fontSize: 11.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }
}
